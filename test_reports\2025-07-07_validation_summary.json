{"player_props": {"points": {"mae": 1.05, "accuracy": 50.0, "within_3": 100.0}, "rebounds": {"mae": 0.35, "accuracy": 100.0, "within_2": 100.0}, "assists": {"mae": 0.18, "accuracy": 100.0, "within_2": 100.0}, "blocks": {"mae": 0.2, "accuracy": 100.0, "within_1": 100.0}, "threes": {"mae": 0.35, "accuracy": 100.0, "within_2": 100.0}, "steals": {"mae": 0.8, "accuracy": 100.0, "within_1": 100.0}}, "game_outcomes": {"moneyline": {"accuracy": 0.0, "confidence": 15.0}, "spread": {"mae": 19.0, "cover_accuracy": 0.0}, "total": {"mae": 4.5, "over_accuracy": 100.0}}, "overall": {"total_predictions": 22, "player_prop_count": 16, "game_outcome_count": 6, "avg_player_error": 0.5, "avg_game_error": 7.95, "best_predictions": [{"type": "player_prop", "details": {"player": "<PERSON><PERSON><PERSON>", "stat_type": "rebounds", "predicted": 4.1, "actual": 4, "error": 0.09999999999999964}}], "worst_predictions": [{"type": "player_prop", "details": {"player": "<PERSON><PERSON><PERSON><PERSON>", "stat_type": "points", "predicted": 24.5, "actual": 26, "error": 1.5}}]}, "insights": [{"type": "warning", "category": "player_props", "message": "Points model accuracy (50.0%) below 60% threshold", "recommendation": "Consider retraining points model with recent data"}, {"type": "success", "category": "player_props", "message": "Rebounds model performing excellently (100.0%)", "recommendation": "Maintain current model parameters"}, {"type": "success", "category": "player_props", "message": "Assists model performing excellently (100.0%)", "recommendation": "Maintain current model parameters"}, {"type": "success", "category": "player_props", "message": "Blocks model performing excellently (100.0%)", "recommendation": "Maintain current model parameters"}, {"type": "success", "category": "player_props", "message": "Threes model performing excellently (100.0%)", "recommendation": "Maintain current model parameters"}, {"type": "success", "category": "player_props", "message": "Steals model performing excellently (100.0%)", "recommendation": "Maintain current model parameters"}, {"type": "warning", "category": "game_outcomes", "message": "Moneyline predictions below 55% accuracy", "recommendation": "Review moneyline model features and training data"}, {"type": "warning", "category": "game_outcomes", "message": "Spread predictions below 55% accuracy", "recommendation": "Review spread model features and training data"}]}