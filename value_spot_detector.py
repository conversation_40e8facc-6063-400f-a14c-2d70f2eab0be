#!/usr/bin/env python3
"""
🚀 HYPER_MEDUSA_NEURAL_VAULT - VALUE SPOT DETECTOR
Advanced value identification system for profitable betting opportunities
Target: 15%+ ROI through systematic value detection
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class ValueSpotDetector:
    """Advanced value spot detection for betting opportunities"""
    
    def __init__(self, min_edge: float = 0.05, min_confidence: float = 0.65):
        self.min_edge = min_edge  # Minimum 5% edge required
        self.min_confidence = min_confidence  # Minimum 65% confidence
        self.value_spots = []
        self.historical_performance = {}
        self.market_inefficiencies = {}
        
    def detect_value_spots(self, predictions: Dict, market_odds: Dict) -> List[Dict]:
        """Detect value betting opportunities"""
        value_spots = []
        
        for bet_type, prediction_data in predictions.items():
            if bet_type in market_odds:
                market_data = market_odds[bet_type]
                
                # Calculate value for each prediction
                for player_stat, pred_info in prediction_data.items():
                    if player_stat in market_data:
                        value_analysis = self._analyze_value(
                            pred_info, 
                            market_data[player_stat],
                            bet_type,
                            player_stat
                        )
                        
                        if value_analysis['has_value']:
                            value_spots.append(value_analysis)
        
        # Sort by expected value (highest first)
        value_spots.sort(key=lambda x: x['expected_value'], reverse=True)
        
        self.value_spots = value_spots
        return value_spots
    
    def _analyze_value(self, prediction: Dict, market: Dict, bet_type: str, player_stat: str) -> Dict:
        """Analyze value for a specific bet"""
        try:
            # Extract prediction data
            pred_value = prediction.get('prediction', 0)
            pred_confidence = prediction.get('confidence', 0.5)
            pred_probability = prediction.get('probability', 0.5)
            
            # Extract market data
            over_odds = market.get('over_odds', -110)
            under_odds = market.get('under_odds', -110)
            line = market.get('line', pred_value)
            
            # Convert American odds to implied probability
            over_implied_prob = self._american_to_probability(over_odds)
            under_implied_prob = self._american_to_probability(under_odds)
            
            # Calculate our true probability
            if pred_value > line:
                our_prob = pred_probability
                market_prob = over_implied_prob
                bet_side = 'over'
                odds = over_odds
            else:
                our_prob = 1 - pred_probability
                market_prob = under_implied_prob
                bet_side = 'under'
                odds = under_odds
            
            # Calculate edge and expected value
            edge = our_prob - market_prob
            expected_value = (our_prob * self._odds_to_payout(odds)) - (1 - our_prob)
            
            # Determine if this is a value bet
            has_value = (
                edge >= self.min_edge and 
                pred_confidence >= self.min_confidence and
                expected_value > 0
            )
            
            # Calculate Kelly Criterion bet size
            kelly_fraction = self._calculate_kelly(our_prob, odds) if has_value else 0
            
            # Risk assessment
            risk_level = self._assess_risk(prediction, market, bet_type)
            
            return {
                'bet_type': bet_type,
                'player_stat': player_stat,
                'bet_side': bet_side,
                'line': line,
                'prediction': pred_value,
                'our_probability': our_prob,
                'market_probability': market_prob,
                'edge': edge,
                'expected_value': expected_value,
                'confidence': pred_confidence,
                'odds': odds,
                'kelly_fraction': kelly_fraction,
                'risk_level': risk_level,
                'has_value': has_value,
                'value_score': self._calculate_value_score(edge, expected_value, pred_confidence),
                'recommendation': self._generate_recommendation(has_value, edge, expected_value, risk_level)
            }
            
        except Exception as e:
            print(f"⚠️ Error analyzing value for {player_stat}: {e}")
            return {
                'bet_type': bet_type,
                'player_stat': player_stat,
                'has_value': False,
                'error': str(e)
            }
    
    def _american_to_probability(self, odds: int) -> float:
        """Convert American odds to implied probability"""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)
    
    def _odds_to_payout(self, odds: int) -> float:
        """Convert American odds to payout multiplier"""
        if odds > 0:
            return odds / 100
        else:
            return 100 / abs(odds)
    
    def _calculate_kelly(self, probability: float, odds: int) -> float:
        """Calculate Kelly Criterion bet size"""
        try:
            payout = self._odds_to_payout(odds)
            kelly = (probability * (payout + 1) - 1) / payout
            return max(0, min(0.25, kelly))  # Cap at 25% of bankroll
        except:
            return 0
    
    def _assess_risk(self, prediction: Dict, market: Dict, bet_type: str) -> str:
        """Assess risk level of the bet"""
        confidence = prediction.get('confidence', 0.5)
        variance = prediction.get('variance', 1.0)
        
        if confidence >= 0.8 and variance <= 0.5:
            return 'LOW'
        elif confidence >= 0.65 and variance <= 1.0:
            return 'MEDIUM'
        else:
            return 'HIGH'
    
    def _calculate_value_score(self, edge: float, expected_value: float, confidence: float) -> float:
        """Calculate composite value score"""
        return (edge * 0.4 + expected_value * 0.4 + confidence * 0.2) * 100
    
    def _generate_recommendation(self, has_value: bool, edge: float, expected_value: float, risk_level: str) -> str:
        """Generate betting recommendation"""
        if not has_value:
            return "❌ NO VALUE - Skip this bet"
        
        if expected_value >= 0.15 and edge >= 0.10:
            return f"🔥 STRONG VALUE - {risk_level} risk"
        elif expected_value >= 0.08 and edge >= 0.06:
            return f"✅ GOOD VALUE - {risk_level} risk"
        else:
            return f"⚠️ MARGINAL VALUE - {risk_level} risk"
    
    def generate_value_report(self) -> Dict:
        """Generate comprehensive value betting report"""
        if not self.value_spots:
            return {
                'total_opportunities': 0,
                'message': 'No value spots detected'
            }
        
        # Categorize value spots
        strong_value = [spot for spot in self.value_spots if spot['expected_value'] >= 0.15]
        good_value = [spot for spot in self.value_spots if 0.08 <= spot['expected_value'] < 0.15]
        marginal_value = [spot for spot in self.value_spots if 0.02 <= spot['expected_value'] < 0.08]
        
        # Calculate portfolio metrics
        total_ev = sum(spot['expected_value'] for spot in self.value_spots)
        avg_edge = np.mean([spot['edge'] for spot in self.value_spots])
        avg_confidence = np.mean([spot['confidence'] for spot in self.value_spots])
        
        # Risk distribution
        risk_distribution = {
            'LOW': len([s for s in self.value_spots if s['risk_level'] == 'LOW']),
            'MEDIUM': len([s for s in self.value_spots if s['risk_level'] == 'MEDIUM']),
            'HIGH': len([s for s in self.value_spots if s['risk_level'] == 'HIGH'])
        }
        
        return {
            'total_opportunities': len(self.value_spots),
            'strong_value_count': len(strong_value),
            'good_value_count': len(good_value),
            'marginal_value_count': len(marginal_value),
            'portfolio_expected_value': total_ev,
            'average_edge': avg_edge,
            'average_confidence': avg_confidence,
            'risk_distribution': risk_distribution,
            'top_opportunities': self.value_spots[:5],  # Top 5 value spots
            'recommended_bankroll_allocation': self._calculate_portfolio_allocation()
        }
    
    def _calculate_portfolio_allocation(self) -> Dict:
        """Calculate recommended bankroll allocation across value spots"""
        total_kelly = sum(spot['kelly_fraction'] for spot in self.value_spots)
        
        if total_kelly > 1.0:
            # Scale down if total Kelly exceeds 100%
            scale_factor = 0.8 / total_kelly
            allocations = {
                spot['player_stat']: spot['kelly_fraction'] * scale_factor 
                for spot in self.value_spots
            }
        else:
            allocations = {
                spot['player_stat']: spot['kelly_fraction'] 
                for spot in self.value_spots
            }
        
        return {
            'individual_allocations': allocations,
            'total_allocation': sum(allocations.values()),
            'remaining_bankroll': 1.0 - sum(allocations.values())
        }
    
    def simulate_betting_performance(self, num_simulations: int = 1000) -> Dict:
        """Simulate betting performance based on detected value spots"""
        if not self.value_spots:
            return {'error': 'No value spots to simulate'}
        
        results = []
        
        for _ in range(num_simulations):
            portfolio_return = 0
            
            for spot in self.value_spots:
                # Simulate bet outcome based on our probability
                bet_wins = np.random.random() < spot['our_probability']
                bet_size = spot['kelly_fraction']
                
                if bet_wins:
                    payout = self._odds_to_payout(spot['odds'])
                    portfolio_return += bet_size * payout
                else:
                    portfolio_return -= bet_size
            
            results.append(portfolio_return)
        
        results = np.array(results)
        
        return {
            'mean_return': np.mean(results),
            'median_return': np.median(results),
            'std_return': np.std(results),
            'win_probability': np.mean(results > 0),
            'percentile_5': np.percentile(results, 5),
            'percentile_95': np.percentile(results, 95),
            'max_drawdown': np.min(results),
            'max_return': np.max(results),
            'sharpe_ratio': np.mean(results) / np.std(results) if np.std(results) > 0 else 0
        }

def main():
    print("🚀 VALUE SPOT DETECTOR")
    print("=" * 30)
    
    # Initialize detector
    detector = ValueSpotDetector(min_edge=0.05, min_confidence=0.65)
    
    # Sample predictions and market odds
    sample_predictions = {
        'player_props': {
            'A_Wilson_points': {
                'prediction': 18.5,
                'confidence': 0.72,
                'probability': 0.68
            },
            'B_Stewart_rebounds': {
                'prediction': 8.2,
                'confidence': 0.78,
                'probability': 0.71
            },
            'S_Diggins_assists': {
                'prediction': 6.1,
                'confidence': 0.65,
                'probability': 0.63
            }
        }
    }
    
    sample_market_odds = {
        'player_props': {
            'A_Wilson_points': {
                'line': 17.5,
                'over_odds': -110,
                'under_odds': -110
            },
            'B_Stewart_rebounds': {
                'line': 7.5,
                'over_odds': -105,
                'under_odds': -115
            },
            'S_Diggins_assists': {
                'line': 6.5,
                'over_odds': -120,
                'under_odds': +100
            }
        }
    }
    
    print("\n🧪 DETECTING VALUE SPOTS")
    print("-" * 25)
    
    # Detect value spots
    value_spots = detector.detect_value_spots(sample_predictions, sample_market_odds)
    
    print(f"   Value Opportunities Found: {len(value_spots)}")
    
    for spot in value_spots:
        if spot['has_value']:
            print(f"   ✅ {spot['player_stat']}: {spot['recommendation']}")
            print(f"      Edge: {spot['edge']:.1%}, EV: {spot['expected_value']:+.3f}")
    
    # Generate report
    print("\n📊 VALUE REPORT")
    print("-" * 15)
    
    report = detector.generate_value_report()
    print(f"   Total Opportunities: {report['total_opportunities']}")
    print(f"   Portfolio EV: {report.get('portfolio_expected_value', 0):+.3f}")
    print(f"   Average Edge: {report.get('average_edge', 0):.1%}")
    
    # Simulate performance
    if value_spots:
        print("\n🎲 PERFORMANCE SIMULATION")
        print("-" * 22)
        
        simulation = detector.simulate_betting_performance(1000)
        print(f"   Expected Return: {simulation['mean_return']:+.1%}")
        print(f"   Win Probability: {simulation['win_probability']:.1%}")
        print(f"   Sharpe Ratio: {simulation['sharpe_ratio']:.2f}")
    
    print("\n✅ VALUE SPOT DETECTOR READY")
    print(f"   🎯 Target ROI: 15%+")
    print(f"   📊 Min Edge Required: {detector.min_edge:.1%}")

if __name__ == "__main__":
    main()
