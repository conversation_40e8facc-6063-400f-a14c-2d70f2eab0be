{"models": {"step1_points": {"path": "models\\player_points_model.ckpt", "type": "pytorch_lightning", "description": "Step 1: Player Points Model", "required": true, "expected_size_mb": 2.3, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 27, "metadata": {"epoch": 15, "has_state_dict": true, "param_count": 25, "loadable": true}}, "step2_totals": {"path": "models\\game_totals_model.ckpt", "type": "pytorch_lightning", "description": "Step 2: Game Totals Model", "required": true, "expected_size_mb": 1.4, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 22, "metadata": {"epoch": 47, "has_state_dict": true, "param_count": 41, "loadable": true}}, "step3_moneyline": {"path": "models\\moneyline_model.ckpt", "type": "pytorch_lightning", "description": "Step 3: Moneyline Model", "required": true, "expected_size_mb": 0.2, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 15, "metadata": {"loadable": true}}, "step4_rebounds": {"path": "models\\rebounds_model-v1.ckpt", "type": "pytorch_lightning", "description": "Step 4: Player Rebounds Model", "required": true, "expected_size_mb": 1.8, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 17, "metadata": {"epoch": 43, "has_state_dict": true, "param_count": 27, "loadable": true}}, "step5_assists": {"path": "models\\assists_model-v1.ckpt", "type": "pytorch_lightning", "description": "Step 5: Player Assists Model", "required": true, "expected_size_mb": 0.6, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 15, "metadata": {"epoch": 74, "has_state_dict": true, "param_count": 18, "loadable": true}}, "step6_assists_v2": {"path": "models\\assists_model-v1.ckpt", "type": "pytorch_lightning", "description": "Step 6: <PERSON><PERSON><PERSON> Assists Model", "required": true, "expected_size_mb": 0.6, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 15, "metadata": {"epoch": 74, "has_state_dict": true, "param_count": 18, "loadable": true}}, "step7_threes": {"path": "models\\threes_model-epoch=78-val_loss=0.003.ckpt", "type": "pytorch_lightning", "description": "Step 7: Three-Pointers Model", "required": true, "expected_size_mb": 0.6, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 15, "metadata": {"epoch": 78, "has_state_dict": true, "param_count": 18, "loadable": true}}, "step8_steals": {"path": "models\\step8_steals_model-epoch=97-val_loss=0.001.ckpt", "type": "pytorch_lightning", "description": "Step 8: St<PERSON>s Model", "required": true, "expected_size_mb": 0.6, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 25, "metadata": {"epoch": 97, "has_state_dict": true, "param_count": 25, "loadable": true}}, "step8_blocks": {"path": "models\\blocks_model.ckpt", "type": "pytorch_lightning", "description": "Step 8: Blocks Model", "required": true, "expected_size_mb": 0.6, "last_trained": "2025-07-07", "auto_mapped": true, "mapping_score": 15, "metadata": {"epoch": 99, "has_state_dict": true, "param_count": 25, "loadable": true}}}, "verification": {"check_file_exists": true, "check_file_size": true, "check_pytorch_load": true, "check_model_structure": true, "require_all_models": true}, "discovery": {"last_discovery": "2025-07-07T20:03:02.016736", "total_files_found": 41, "auto_mapped": 9}}