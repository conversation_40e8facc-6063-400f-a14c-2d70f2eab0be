#!/usr/bin/env python3
"""
🛡️ BULLETPROOF MODEL LOADER
100% reliable model loading with zero mistakes guaranteed
"""

import torch
import json
from pathlib import Path
from datetime import datetime
import logging
from model_loader_guardian import ModelGuardian

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BulletproofModelLoader:
    """Bulletproof model loading system with comprehensive safety checks"""
    
    def __init__(self):
        self.guardian = ModelGuardian()
        self.loaded_models = {}
        self.loading_log = []
        self.safety_checks_enabled = True
        
    def enable_safety_mode(self):
        """Enable all safety checks (default)"""
        self.safety_checks_enabled = True
        logger.info("🛡️ Safety mode ENABLED - All checks active")
    
    def disable_safety_mode(self):
        """Disable safety checks (NOT RECOMMENDED)"""
        self.safety_checks_enabled = False
        logger.warning("⚠️ Safety mode DISABLED - Use with extreme caution")
    
    def pre_flight_check(self):
        """Comprehensive pre-flight safety check"""
        logger.info("🚀 BULLETPROOF LOADER PRE-FLIGHT CHECK")
        logger.info("=" * 50)
        
        if not self.safety_checks_enabled:
            logger.warning("⚠️ SAFETY CHECKS DISABLED - PROCEEDING WITHOUT VERIFICATION")
            return True
        
        # Run Guardian verification
        verification_results = self.guardian.verify_all_models()
        
        # Check results
        if verification_results['failed'] > 0:
            logger.error(f"❌ PRE-FLIGHT FAILED: {verification_results['failed']} models failed verification")
            
            # Show failed models
            for detail in verification_results['details']:
                if detail['status'] == 'FAILED':
                    logger.error(f"   ❌ {detail['model_key']}: {detail['errors']}")
            
            return False
        
        if verification_results['warnings'] > 0:
            logger.warning(f"⚠️ PRE-FLIGHT WARNINGS: {verification_results['warnings']} models have warnings")
            
            # Show warnings
            for detail in verification_results['details']:
                if detail['status'] == 'WARNING':
                    logger.warning(f"   ⚠️ {detail['model_key']}: {detail['warnings']}")
        
        logger.info(f"✅ PRE-FLIGHT PASSED: {verification_results['passed']}/{verification_results['total_models']} models verified")
        return True
    
    def load_model_with_fallback(self, model_key, fallback_keys=None):
        """Load model with automatic fallback options"""
        if fallback_keys is None:
            fallback_keys = []
        
        # Try primary model
        try:
            return self.load_single_model(model_key)
        except Exception as e:
            logger.warning(f"⚠️ Primary model {model_key} failed: {e}")
            
            # Try fallbacks
            for fallback_key in fallback_keys:
                try:
                    logger.info(f"🔄 Trying fallback: {fallback_key}")
                    return self.load_single_model(fallback_key)
                except Exception as fallback_e:
                    logger.warning(f"⚠️ Fallback {fallback_key} failed: {fallback_e}")
            
            # All options failed
            raise RuntimeError(f"All loading options failed for {model_key}")
    
    def load_single_model(self, model_key):
        """Load a single model with comprehensive safety checks"""
        start_time = datetime.now()
        
        try:
            # Safety check
            if self.safety_checks_enabled:
                verification = self.guardian.verify_model_file(
                    model_key, 
                    self.guardian.registry['models'][model_key]
                )
                
                if verification['status'] == 'FAILED':
                    raise RuntimeError(f"Safety check failed: {verification['errors']}")
            
            # Load using Guardian
            checkpoint = self.guardian.safe_load_model(model_key)
            
            # Additional validation
            if isinstance(checkpoint, dict):
                # Check for required components
                required_components = ['state_dict']
                missing_components = [comp for comp in required_components if comp not in checkpoint]
                
                if missing_components:
                    logger.warning(f"⚠️ Missing components in {model_key}: {missing_components}")
                    # For some models, this might be OK (e.g., direct state dict saves)
                    if 'state_dict' not in checkpoint:
                        # Try to use the checkpoint directly as state_dict
                        if all(isinstance(v, torch.Tensor) for v in checkpoint.values()):
                            checkpoint = {'state_dict': checkpoint}
                            logger.info(f"✅ Converted direct state_dict for {model_key}")
            
            # Store in cache
            self.loaded_models[model_key] = {
                'checkpoint': checkpoint,
                'loaded_at': start_time.isoformat(),
                'load_time_ms': (datetime.now() - start_time).total_seconds() * 1000,
                'verified': self.safety_checks_enabled
            }
            
            # Log successful load
            load_time = (datetime.now() - start_time).total_seconds() * 1000
            self.loading_log.append({
                'model_key': model_key,
                'status': 'SUCCESS',
                'load_time_ms': load_time,
                'timestamp': start_time.isoformat()
            })
            
            logger.info(f"✅ Successfully loaded {model_key} ({load_time:.1f}ms)")
            return checkpoint
            
        except Exception as e:
            # Log failed load
            load_time = (datetime.now() - start_time).total_seconds() * 1000
            self.loading_log.append({
                'model_key': model_key,
                'status': 'FAILED',
                'error': str(e),
                'load_time_ms': load_time,
                'timestamp': start_time.isoformat()
            })
            
            logger.error(f"❌ Failed to load {model_key}: {e}")
            raise
    
    def load_all_models(self, required_models=None):
        """Load all required models with comprehensive error handling"""
        if required_models is None:
            required_models = list(self.guardian.registry['models'].keys())
        
        logger.info(f"🚀 LOADING {len(required_models)} MODELS")
        logger.info("=" * 40)
        
        # Pre-flight check
        if not self.pre_flight_check():
            raise RuntimeError("Pre-flight check failed - aborting model loading")
        
        # Define fallback strategies
        fallback_strategies = {
            'step3_moneyline': ['step3_moneyline'],  # No fallbacks for now
            'step6_assists_v2': ['step5_assists'],  # Can fallback to v1
            'step8_blocks': ['step8_steals'],  # Similar models
        }
        
        loaded_count = 0
        failed_models = []
        
        for model_key in required_models:
            try:
                fallbacks = fallback_strategies.get(model_key, [])
                self.load_model_with_fallback(model_key, fallbacks)
                loaded_count += 1
            except Exception as e:
                logger.error(f"❌ CRITICAL: Failed to load {model_key}: {e}")
                failed_models.append(model_key)
        
        # Final status
        success_rate = (loaded_count / len(required_models)) * 100
        
        logger.info(f"\n📊 LOADING SUMMARY:")
        logger.info(f"   📊 Total Models: {len(required_models)}")
        logger.info(f"   ✅ Loaded: {loaded_count}")
        logger.info(f"   ❌ Failed: {len(failed_models)}")
        logger.info(f"   📈 Success Rate: {success_rate:.1f}%")
        
        if failed_models:
            logger.error(f"   🚨 Failed Models: {failed_models}")
        
        if success_rate == 100:
            logger.info(f"   🎯 Status: ✅ ALL MODELS LOADED SUCCESSFULLY")
            return True
        elif success_rate >= 80:
            logger.warning(f"   🎯 Status: ⚠️ PARTIAL SUCCESS - SOME MODELS MISSING")
            return False
        else:
            logger.error(f"   🎯 Status: ❌ CRITICAL FAILURE - INSUFFICIENT MODELS")
            raise RuntimeError(f"Critical model loading failure: {len(failed_models)} models failed")
    
    def get_model(self, model_key):
        """Get a loaded model safely"""
        if model_key not in self.loaded_models:
            raise KeyError(f"Model {model_key} not loaded. Call load_all_models() first.")
        
        return self.loaded_models[model_key]['checkpoint']
    
    def get_loading_report(self):
        """Get comprehensive loading report"""
        return {
            'total_models_loaded': len(self.loaded_models),
            'loaded_models': list(self.loaded_models.keys()),
            'loading_log': self.loading_log,
            'safety_checks_enabled': self.safety_checks_enabled,
            'last_load_time': datetime.now().isoformat()
        }
    
    def validate_loaded_models(self):
        """Validate all loaded models are working correctly"""
        logger.info("🔍 VALIDATING LOADED MODELS")
        logger.info("=" * 30)
        
        validation_results = {
            'total': len(self.loaded_models),
            'valid': 0,
            'invalid': 0,
            'details': []
        }
        
        for model_key, model_info in self.loaded_models.items():
            try:
                checkpoint = model_info['checkpoint']
                
                # Basic validation
                is_valid = True
                validation_notes = []
                
                if isinstance(checkpoint, dict):
                    if 'state_dict' in checkpoint:
                        state_dict = checkpoint['state_dict']
                        param_count = len(state_dict)
                        validation_notes.append(f"{param_count} parameters")
                    else:
                        # Direct state dict
                        param_count = len(checkpoint)
                        validation_notes.append(f"{param_count} parameters (direct)")
                    
                    if param_count < 5:
                        is_valid = False
                        validation_notes.append("Too few parameters")
                else:
                    is_valid = False
                    validation_notes.append("Invalid checkpoint format")
                
                if is_valid:
                    validation_results['valid'] += 1
                    logger.info(f"   ✅ {model_key}: Valid ({', '.join(validation_notes)})")
                else:
                    validation_results['invalid'] += 1
                    logger.error(f"   ❌ {model_key}: Invalid ({', '.join(validation_notes)})")
                
                validation_results['details'].append({
                    'model_key': model_key,
                    'valid': is_valid,
                    'notes': validation_notes
                })
                
            except Exception as e:
                validation_results['invalid'] += 1
                logger.error(f"   ❌ {model_key}: Validation error - {e}")
                validation_results['details'].append({
                    'model_key': model_key,
                    'valid': False,
                    'error': str(e)
                })
        
        success_rate = (validation_results['valid'] / validation_results['total']) * 100
        logger.info(f"\n📊 VALIDATION SUMMARY:")
        logger.info(f"   ✅ Valid: {validation_results['valid']}")
        logger.info(f"   ❌ Invalid: {validation_results['invalid']}")
        logger.info(f"   📈 Success Rate: {success_rate:.1f}%")
        
        return validation_results

def main():
    """Test the bulletproof loader"""
    loader = BulletproofModelLoader()
    
    try:
        # Load all models
        success = loader.load_all_models()
        
        if success:
            # Validate loaded models
            validation = loader.validate_loaded_models()
            
            # Show final report
            report = loader.get_loading_report()
            print(f"\n🎯 FINAL REPORT:")
            print(f"   📊 Models Loaded: {report['total_models_loaded']}")
            print(f"   🛡️ Safety Checks: {'✅' if report['safety_checks_enabled'] else '❌'}")
            print(f"   ✅ Valid Models: {validation['valid']}")
            print(f"   📈 Overall Success: {(validation['valid']/len(loader.guardian.registry['models'])*100):.1f}%")
            
            if validation['valid'] == len(loader.guardian.registry['models']):
                print(f"   🎯 Status: ✅ BULLETPROOF LOADING SUCCESSFUL")
            else:
                print(f"   🎯 Status: ⚠️ PARTIAL SUCCESS")
        
    except Exception as e:
        print(f"❌ BULLETPROOF LOADING FAILED: {e}")

if __name__ == "__main__":
    main()
