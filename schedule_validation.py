#!/usr/bin/env python3
"""
⏰ DAILY PREDICTION VALIDATION SCHEDULER
Automated scheduling system for daily validation reports
"""

import os
import time
import schedule
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from pathlib import Path
import json
import logging
from prediction_analyzer import PredictionValidator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/validation_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ValidationScheduler:
    """Automated validation scheduler with email reporting"""
    
    def __init__(self, config_file='config/validation_config.json'):
        self.config = self.load_config(config_file)
        self.validator = PredictionValidator(
            prediction_dir=self.config.get('prediction_dir', 'predictions/daily'),
            boxscore_dir=self.config.get('boxscore_dir', 'data/boxscores'),
            output_dir=self.config.get('output_dir', 'reports/validation')
        )
        
    def load_config(self, config_file):
        """Load validation configuration"""
        default_config = {
            'email': {
                'enabled': False,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'recipients': []
            },
            'validation': {
                'retroactive_days': 7,
                'auto_update_models': False,
                'notification_thresholds': {
                    'accuracy_drop': 5.0,
                    'error_increase': 2.0
                }
            },
            'schedule': {
                'daily_time': '03:00',
                'timezone': 'UTC'
            }
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
        except Exception as e:
            logger.warning(f"Could not load config file: {e}. Using defaults.")
        
        return default_config
    
    def run_daily_validation(self):
        """Run daily validation process"""
        logger.info("🚀 Starting daily validation process...")
        
        try:
            # Validate yesterday's predictions
            yesterday = datetime.now() - timedelta(days=1)
            results = self.validator.run_validation(yesterday)
            
            if results:
                # Generate summary
                summary = self.generate_daily_summary(results, yesterday)
                
                # Send email if configured
                if self.config['email']['enabled']:
                    self.send_email_report(summary, results, yesterday)
                
                # Check for model issues
                issues = self.check_model_issues(results)
                if issues:
                    logger.warning(f"⚠️ Model issues detected: {len(issues)}")
                    for issue in issues:
                        logger.warning(f"   - {issue}")
                
                # Auto-update models if enabled
                if self.config['validation']['auto_update_models'] and issues:
                    self.trigger_model_updates(issues)
                
                logger.info("✅ Daily validation completed successfully")
                return True
            else:
                logger.warning("⚠️ No validation results generated")
                return False
                
        except Exception as e:
            logger.error(f"❌ Daily validation failed: {e}")
            return False
    
    def generate_daily_summary(self, results, date):
        """Generate daily validation summary"""
        date_str = date.strftime('%Y-%m-%d')
        
        summary = {
            'date': date_str,
            'timestamp': datetime.now().isoformat(),
            'overall_performance': results['overall'],
            'player_props_summary': {},
            'game_outcomes_summary': {},
            'key_insights': results['insights'],
            'recommendations': []
        }
        
        # Player props summary
        if results['player_props']:
            total_accuracy = 0
            count = 0
            for stat_type, metrics in results['player_props'].items():
                total_accuracy += metrics['accuracy']
                count += 1
            
            summary['player_props_summary'] = {
                'average_accuracy': round(total_accuracy / count, 1) if count > 0 else 0,
                'best_stat': max(results['player_props'].items(), key=lambda x: x[1]['accuracy'])[0] if results['player_props'] else None,
                'worst_stat': min(results['player_props'].items(), key=lambda x: x[1]['accuracy'])[0] if results['player_props'] else None
            }
        
        # Game outcomes summary
        if results['game_outcomes']:
            summary['game_outcomes_summary'] = {
                'outcomes_tested': len(results['game_outcomes']),
                'best_outcome': max(results['game_outcomes'].items(), 
                                  key=lambda x: x[1].get('accuracy', x[1].get('cover_accuracy', 0)))[0] if results['game_outcomes'] else None
            }
        
        # Generate recommendations
        summary['recommendations'] = self.generate_recommendations(results)
        
        return summary
    
    def generate_recommendations(self, results):
        """Generate actionable recommendations"""
        recommendations = []
        
        # Player props recommendations
        if results['player_props']:
            for stat_type, metrics in results['player_props'].items():
                if metrics['accuracy'] < 60:
                    recommendations.append({
                        'priority': 'high',
                        'category': 'model_performance',
                        'action': f"Retrain {stat_type} model",
                        'reason': f"Accuracy ({metrics['accuracy']}%) below 60% threshold"
                    })
                elif metrics['mae'] > 3.0 and stat_type == 'points':
                    recommendations.append({
                        'priority': 'medium',
                        'category': 'model_tuning',
                        'action': f"Tune {stat_type} model parameters",
                        'reason': f"MAE ({metrics['mae']}) above 3.0 threshold"
                    })
        
        # Game outcomes recommendations
        if results['game_outcomes']:
            for outcome_type, metrics in results['game_outcomes'].items():
                accuracy_key = 'accuracy' if 'accuracy' in metrics else 'cover_accuracy'
                if accuracy_key in metrics and metrics[accuracy_key] < 55:
                    recommendations.append({
                        'priority': 'high',
                        'category': 'model_performance',
                        'action': f"Review {outcome_type} model features",
                        'reason': f"Accuracy ({metrics[accuracy_key]}%) below 55% threshold"
                    })
        
        return recommendations
    
    def check_model_issues(self, results):
        """Check for model performance issues"""
        issues = []
        thresholds = self.config['validation']['notification_thresholds']
        
        # Check player props
        if results['player_props']:
            for stat_type, metrics in results['player_props'].items():
                if metrics['accuracy'] < (100 - thresholds['accuracy_drop']):
                    issues.append(f"{stat_type} model accuracy below threshold: {metrics['accuracy']}%")
                
                if metrics['mae'] > thresholds['error_increase']:
                    issues.append(f"{stat_type} model MAE above threshold: {metrics['mae']}")
        
        # Check game outcomes
        if results['game_outcomes']:
            for outcome_type, metrics in results['game_outcomes'].items():
                accuracy_key = 'accuracy' if 'accuracy' in metrics else 'cover_accuracy'
                if accuracy_key in metrics and metrics[accuracy_key] < 55:
                    issues.append(f"{outcome_type} outcome accuracy below 55%: {metrics[accuracy_key]}%")
        
        return issues
    
    def trigger_model_updates(self, issues):
        """Trigger automatic model updates"""
        logger.info("🔄 Triggering automatic model updates...")
        
        # This would integrate with your model training pipeline
        # For now, just log the actions that would be taken
        for issue in issues:
            if 'points' in issue.lower():
                logger.info("   - Would retrain points model")
            elif 'rebounds' in issue.lower():
                logger.info("   - Would retrain rebounds model")
            elif 'assists' in issue.lower():
                logger.info("   - Would retrain assists model")
            elif 'moneyline' in issue.lower():
                logger.info("   - Would retrain moneyline model")
            elif 'spread' in issue.lower():
                logger.info("   - Would retrain spread model")
            elif 'total' in issue.lower():
                logger.info("   - Would retrain totals model")
    
    def send_email_report(self, summary, results, date):
        """Send email validation report"""
        try:
            email_config = self.config['email']
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = f"🏀 HYPER_MEDUSA_NEURAL_VAULT Daily Report - {date.strftime('%Y-%m-%d')}"
            
            # Create email body
            body = self.create_email_body(summary, results)
            msg.attach(MIMEText(body, 'html'))
            
            # Attach validation report if it exists
            report_path = Path(self.validator.output_dir) / f"{date.strftime('%Y-%m-%d')}_validation_report.html"
            if report_path.exists():
                with open(report_path, 'rb') as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {report_path.name}'
                    )
                    msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            text = msg.as_string()
            server.sendmail(email_config['username'], email_config['recipients'], text)
            server.quit()
            
            logger.info(f"📧 Email report sent to {len(email_config['recipients'])} recipients")
            
        except Exception as e:
            logger.error(f"❌ Failed to send email report: {e}")
    
    def create_email_body(self, summary, results):
        """Create HTML email body"""
        date_str = summary['date']
        
        # Calculate overall accuracy
        player_accuracy = summary['player_props_summary'].get('average_accuracy', 0)
        
        # Determine status emoji
        if player_accuracy >= 75:
            status_emoji = "🟢"
            status_text = "EXCELLENT"
        elif player_accuracy >= 65:
            status_emoji = "🟡"
            status_text = "GOOD"
        else:
            status_emoji = "🔴"
            status_text = "NEEDS ATTENTION"
        
        body = f"""
        <html>
        <body style="font-family: Arial, sans-serif;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px;">
                <h1>🏀 HYPER_MEDUSA_NEURAL_VAULT</h1>
                <h2>Daily Validation Report - {date_str}</h2>
                <h3>{status_emoji} Status: {status_text}</h3>
            </div>
            
            <div style="margin: 20px 0;">
                <h3>📊 Performance Summary</h3>
                <ul>
                    <li><strong>Total Predictions:</strong> {summary['overall_performance']['total_predictions']}</li>
                    <li><strong>Player Props Accuracy:</strong> {player_accuracy}%</li>
                    <li><strong>Best Performing Stat:</strong> {summary['player_props_summary'].get('best_stat', 'N/A')}</li>
                    <li><strong>Insights Generated:</strong> {len(summary['key_insights'])}</li>
                </ul>
            </div>
            
            <div style="margin: 20px 0;">
                <h3>💡 Key Recommendations</h3>
                <ul>
        """
        
        for rec in summary['recommendations'][:5]:  # Top 5 recommendations
            priority_color = {'high': '#dc3545', 'medium': '#ffc107', 'low': '#28a745'}.get(rec['priority'], '#6c757d')
            body += f'<li style="color: {priority_color};"><strong>{rec["action"]}:</strong> {rec["reason"]}</li>'
        
        body += """
                </ul>
            </div>
            
            <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                <p><em>Full detailed report attached. Generated automatically by HYPER_MEDUSA_NEURAL_VAULT validation system.</em></p>
            </div>
        </body>
        </html>
        """
        
        return body
    
    def start_scheduler(self, daily_time='03:00'):
        """Start the validation scheduler"""
        logger.info(f"⏰ Starting validation scheduler - daily at {daily_time}")
        
        # Schedule daily validation
        schedule.every().day.at(daily_time).do(self.run_daily_validation)
        
        # Run immediately if requested
        logger.info("🚀 Running initial validation...")
        self.run_daily_validation()
        
        # Keep scheduler running
        logger.info("🔄 Scheduler active - waiting for scheduled runs...")
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

def main():
    """Main scheduler function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="⏰ Daily Validation Scheduler")
    parser.add_argument("--time", default="03:00", help="Daily run time (HH:MM)")
    parser.add_argument("--config", default="config/validation_config.json", help="Config file path")
    parser.add_argument("--run-now", action="store_true", help="Run validation immediately")
    parser.add_argument("--email-report", action="store_true", help="Send email report")
    parser.add_argument("--retroactive", type=int, default=1, help="Days back to validate")
    
    args = parser.parse_args()
    
    # Create scheduler
    scheduler = ValidationScheduler(config_file=args.config)
    
    if args.run_now:
        # Run validation immediately
        logger.info("🚀 Running immediate validation...")
        
        for days_back in range(1, args.retroactive + 1):
            validation_date = datetime.now() - timedelta(days=days_back)
            logger.info(f"📅 Validating {validation_date.strftime('%Y-%m-%d')}")
            
            results = scheduler.validator.run_validation(validation_date)
            
            if results and args.email_report:
                summary = scheduler.generate_daily_summary(results, validation_date)
                scheduler.send_email_report(summary, results, validation_date)
    else:
        # Start scheduled runs
        scheduler.start_scheduler(daily_time=args.time)

if __name__ == "__main__":
    main()
