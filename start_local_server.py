#!/usr/bin/env python3
"""
🚀 PRODUCTION LOCAL SERVER LAUNCHER
HYPER_MEDUSA_NEURAL_VAULT RTX 4050 Deployment
"""

import argparse
import sys
import os
import time
import subprocess
from pathlib import Path
from local_config import get_production_config, validate_config

def setup_environment():
    """Setup the local environment"""
    print("🔧 Setting up local environment...")
    
    # Create necessary directories
    directories = ["logs", "cache", "backups", "reports"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ Created directory: {directory}/")
    
    # Check GPU availability
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"   🖥️ GPU Detected: {gpu_name}")
            print(f"   💾 GPU Memory: {gpu_memory:.1f}GB")
        else:
            print("   ⚠️ GPU not available - falling back to CPU")
    except ImportError:
        print("   ❌ PyTorch not installed")
        return False
    
    return True

def check_dependencies():
    """Check if all dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        "torch", "fastapi", "uvicorn", "pydantic", 
        "numpy", "psutil", "requests"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package} - MISSING")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def apply_config(config_file=None):
    """Apply configuration"""
    print("⚙️ Applying configuration...")
    
    if config_file and Path(config_file).exists():
        print(f"   📄 Loading config from: {config_file}")
        # Load custom config
        config = get_production_config()
    else:
        print("   🎯 Using production defaults")
        config = get_production_config()
    
    # Validate configuration
    errors = validate_config(config)
    if errors:
        print("   ❌ Configuration errors:")
        for error in errors:
            print(f"      - {error}")
        return None
    
    print("   ✅ Configuration validated")
    return config

def start_production_server(config, args):
    """Start the production server"""
    print("🚀 Starting HYPER_MEDUSA_NEURAL_VAULT Local Server...")
    
    # Build command
    cmd = [
        "python", "local_prediction_server.py",
        "--config", "local_config.yaml",
        "--port", str(args.port),
        "--gpu-only" if args.gpu_only else "--cpu-fallback",
        "--energy-mode", args.energy_mode
    ]
    
    if args.dashboard:
        print("   📊 Dashboard enabled")
    
    if args.max_rpm:
        print(f"   🎯 Max RPM: {args.max_rpm}")
    
    if args.max_latency:
        print(f"   ⚡ Max Latency: {args.max_latency}")
    
    print(f"   🌐 Server URL: http://localhost:{args.port}")
    print(f"   📊 Dashboard: http://localhost:{args.port}/dashboard")
    print(f"   📚 API Docs: http://localhost:{args.port}/docs")
    
    # Start server
    try:
        if args.production:
            print("\n🎬 LAUNCHING PRODUCTION SERVER...")
            print("=" * 50)
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")
        return False
    
    return True

def run_verification_test(args):
    """Run verification test"""
    print("🧪 Running verification test...")
    
    try:
        result = subprocess.run([
            "python", "test_local_api.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("   ✅ Verification test passed")
            return True
        else:
            print("   ❌ Verification test failed")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ Verification test timed out")
        return False
    except Exception as e:
        print(f"   ❌ Verification test error: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="🚀 HYPER_MEDUSA_NEURAL_VAULT Local Server")
    
    # Server options
    parser.add_argument("--port", type=int, default=8080, help="Server port")
    parser.add_argument("--max-rpm", type=int, help="Maximum requests per minute")
    parser.add_argument("--max-latency", help="Maximum latency (e.g., 5ms)")
    parser.add_argument("--dashboard", action="store_true", help="Enable dashboard")
    parser.add_argument("--energy-saver", action="store_true", help="Enable energy saver mode")
    
    # Configuration options
    parser.add_argument("--config", help="Custom configuration file")
    parser.add_argument("--production", action="store_true", help="Production mode")
    parser.add_argument("--gpu-only", action="store_true", help="GPU only mode")
    parser.add_argument("--energy-mode", default="balanced", choices=["performance", "balanced", "energy_saver"])
    
    # Testing options
    parser.add_argument("--verify", action="store_true", help="Run verification test")
    parser.add_argument("--setup-only", action="store_true", help="Setup environment only")
    
    args = parser.parse_args()
    
    print("🚀 HYPER_MEDUSA_NEURAL_VAULT LOCAL DEPLOYMENT")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        print("❌ Environment setup failed")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        sys.exit(1)
    
    # Apply configuration
    config = apply_config(args.config)
    if not config:
        print("❌ Configuration failed")
        sys.exit(1)
    
    if args.setup_only:
        print("✅ Environment setup complete")
        return
    
    # Run verification test if requested
    if args.verify:
        if not run_verification_test(args):
            print("❌ Verification failed")
            sys.exit(1)
        return
    
    # Start server
    print("\n🎬 STARTING SERVER...")
    print("-" * 30)
    
    success = start_production_server(config, args)
    
    if success:
        print("\n🎊 SERVER DEPLOYMENT COMPLETE!")
        print("   🌐 Access your local HYPER_MEDUSA_NEURAL_VAULT")
        print(f"   📊 Dashboard: http://localhost:{args.port}/dashboard")
    else:
        print("\n❌ SERVER DEPLOYMENT FAILED!")
        sys.exit(1)

if __name__ == "__main__":
    main()
