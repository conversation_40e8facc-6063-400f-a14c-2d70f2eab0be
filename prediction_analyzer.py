#!/usr/bin/env python3
"""
📈 PREDICTION VALIDATION & ANALYSIS SYSTEM
Complete solution for validating HYPER_MEDUSA_NEURAL_VAULT predictions against actual boxscores
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from sklearn.metrics import mean_absolute_error, log_loss, r2_score
import warnings
warnings.filterwarnings('ignore')

from date_manager import DateManager

class ValidationMetrics:
    """Core validation metrics calculator"""
    
    def __init__(self):
        self.metrics = {
            # Player props
            'points': {'mae': 0, 'accuracy': 0, 'within_3': 0},
            'rebounds': {'mae': 0, 'accuracy': 0, 'within_2': 0},
            'assists': {'mae': 0, 'accuracy': 0, 'within_2': 0},
            'steals': {'mae': 0, 'accuracy': 0, 'within_1': 0},
            'blocks': {'mae': 0, 'accuracy': 0, 'within_1': 0},
            'threes': {'mae': 0, 'accuracy': 0, 'within_2': 0},
            
            # Game outcomes
            'moneyline': {'log_loss': 0, 'accuracy': 0, 'confidence': 0},
            'spread': {'mae': 0, 'cover_accuracy': 0, 'push_rate': 0},
            'totals': {'mae': 0, 'over_accuracy': 0, 'push_rate': 0},
            
            # Advanced metrics
            'player_impact': {'correlation': 0, 'r2': 0},
            'lineup_effectiveness': {'r2': 0, 'bias': 0},
            'value_spots': {'hit_rate': 0, 'roi': 0}
        }
    
    def calculate_player_prop_accuracy(self, predictions, actuals, stat_type):
        """Calculate accuracy metrics for player props"""
        if len(predictions) == 0 or len(actuals) == 0:
            return {'mae': 0, 'accuracy': 0, 'within_threshold': 0}
        
        mae = mean_absolute_error(actuals, predictions)
        
        # Define accuracy thresholds by stat type
        thresholds = {
            'points': 3, 'rebounds': 2, 'assists': 2,
            'steals': 1, 'blocks': 1, 'threes': 2
        }
        
        threshold = thresholds.get(stat_type, 2)
        within_threshold = np.mean(np.abs(predictions - actuals) <= threshold) * 100
        
        # Exact accuracy (within 1)
        exact_accuracy = np.mean(np.abs(predictions - actuals) <= 1) * 100
        
        return {
            'mae': round(mae, 2),
            'accuracy': round(exact_accuracy, 1),
            f'within_{threshold}': round(within_threshold, 1)
        }
    
    def calculate_game_outcome_accuracy(self, predictions, actuals, outcome_type):
        """Calculate accuracy metrics for game outcomes"""
        if len(predictions) == 0 or len(actuals) == 0:
            return {'accuracy': 0, 'mae': 0}

        try:
            if outcome_type == 'moneyline':
                # Binary classification accuracy
                accuracy = np.mean(predictions == actuals) * 100
                return {
                    'accuracy': round(accuracy, 1),
                    'confidence': round(np.mean(np.abs(predictions - 0.5)) * 100, 1)
                }

            elif outcome_type in ['spread', 'totals', 'total']:
                mae = mean_absolute_error(actuals, predictions)

                if outcome_type == 'spread':
                    # Cover accuracy (did prediction correctly predict cover)
                    cover_accuracy = np.mean((predictions > 0) == (actuals > 0)) * 100
                    return {
                        'mae': round(mae, 2),
                        'cover_accuracy': round(cover_accuracy, 1)
                    }
                else:  # totals or total
                    # Over/under accuracy
                    over_accuracy = np.mean((predictions > 0) == (actuals > 0)) * 100
                    return {
                        'mae': round(mae, 2),
                        'over_accuracy': round(over_accuracy, 1)
                    }
        except Exception as e:
            print(f"⚠️ Error calculating {outcome_type} accuracy: {e}")
            return {'accuracy': 0, 'mae': 0}

class PredictionValidator:
    """Main prediction validation engine"""
    
    def __init__(self, prediction_dir='predictions/daily', boxscore_dir='data/boxscores', output_dir='reports/validation'):
        self.prediction_dir = Path(prediction_dir)
        self.boxscore_dir = Path(boxscore_dir)
        self.output_dir = Path(output_dir)
        self.metrics_calculator = ValidationMetrics()
        self.date_manager = DateManager()

        # Create directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def load_predictions(self, date):
        """Load predictions for a specific date"""
        date_str = date.strftime('%Y-%m-%d')
        prediction_files = list(self.prediction_dir.glob(f'{date_str}*.json'))
        
        if not prediction_files:
            print(f"⚠️ No predictions found for {date_str}")
            return {}
        
        predictions = {}
        for file_path in prediction_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                predictions.update(data)
            except Exception as e:
                print(f"❌ Error loading {file_path}: {e}")
        
        return predictions
    
    def load_boxscores(self, date):
        """Load boxscores for a specific date"""
        date_str = date.strftime('%Y-%m-%d')

        # Find boxscore files - look for all player files first
        boxscore_files = list(self.boxscore_dir.glob('*_players.parquet'))

        if not boxscore_files:
            # If no parquet files, look for any boxscore files
            boxscore_files = list(self.boxscore_dir.glob('*.parquet'))
            if not boxscore_files:
                print(f"⚠️ No boxscore files found in {self.boxscore_dir}")
                return {}

        # For testing, load all available boxscore files
        # In production, you would filter by actual game date
        boxscores = {}
        for file_path in boxscore_files:
            try:
                if '_players' in file_path.name:
                    game_id = file_path.stem.replace('_players', '')

                    # Load player stats
                    players_df = pd.read_parquet(file_path)

                    # Load team stats if available
                    team_file = file_path.parent / f'{game_id}_teams.parquet'
                    teams_df = pd.read_parquet(team_file) if team_file.exists() else pd.DataFrame()

                    boxscores[game_id] = {
                        'players': players_df,
                        'teams': teams_df
                    }

            except Exception as e:
                print(f"❌ Error loading boxscore {file_path}: {e}")

        if not boxscores:
            print(f"⚠️ No valid boxscores found for {date_str}")
        else:
            print(f"   ✅ Found {len(boxscores)} boxscore(s)")

        return boxscores
    
    def match_predictions_to_actuals(self, predictions, boxscores):
        """Match predictions to actual results"""
        matches = {
            'player_props': [],
            'game_outcomes': [],
            'unmatched_predictions': [],
            'unmatched_boxscores': list(boxscores.keys())
        }

        for pred_key, pred_data in predictions.items():
            matched = False

            # Try to match with boxscore data
            for game_id, boxscore in boxscores.items():
                if self._is_prediction_match(pred_data, boxscore, game_id):

                    # Extract player prop matches
                    if 'player_props' in pred_data:
                        player_matches = self._extract_player_matches(pred_data['player_props'], boxscore['players'])
                        matches['player_props'].extend(player_matches)

                    # Extract game outcome matches
                    if 'game_outcomes' in pred_data:
                        game_matches = self._extract_game_matches(pred_data['game_outcomes'], boxscore['teams'])
                        matches['game_outcomes'].extend(game_matches)

                    # Only remove if game_id is still in the list
                    if game_id in matches['unmatched_boxscores']:
                        matches['unmatched_boxscores'].remove(game_id)
                    matched = True
                    break

            if not matched:
                matches['unmatched_predictions'].append(pred_key)

        return matches
    
    def _is_prediction_match(self, prediction, boxscore, game_id):
        """Check if prediction matches boxscore"""
        # Simple matching logic - can be enhanced
        return True  # For now, assume all predictions match
    
    def _extract_player_matches(self, player_predictions, players_df):
        """Extract player prop prediction matches"""
        matches = []
        
        for player_name, props in player_predictions.items():
            # Find player in boxscore
            player_row = players_df[players_df['PLAYER_NAME'].str.contains(player_name, case=False, na=False)]
            
            if not player_row.empty:
                actual_stats = player_row.iloc[0]
                
                for stat_type, predicted_value in props.items():
                    # Map stat types to boxscore columns
                    stat_mapping = {
                        'points': 'PTS',
                        'rebounds': 'REB', 
                        'assists': 'AST',
                        'steals': 'STL',
                        'blocks': 'BLK',
                        'threes': 'FG3M'
                    }
                    
                    boxscore_col = stat_mapping.get(stat_type)
                    if boxscore_col and boxscore_col in actual_stats:
                        matches.append({
                            'player': player_name,
                            'stat_type': stat_type,
                            'predicted': predicted_value,
                            'actual': actual_stats[boxscore_col],
                            'error': abs(predicted_value - actual_stats[boxscore_col])
                        })
        
        return matches
    
    def _extract_game_matches(self, game_predictions, teams_df):
        """Extract game outcome prediction matches"""
        matches = []
        
        if teams_df.empty or len(teams_df) < 2:
            return matches
        
        # Calculate actual game outcomes
        team1_score = teams_df.iloc[0]['PTS']
        team2_score = teams_df.iloc[1]['PTS']
        total_score = team1_score + team2_score
        point_diff = team1_score - team2_score
        
        for outcome_type, predicted_value in game_predictions.items():
            if outcome_type == 'moneyline':
                actual_winner = 1 if point_diff > 0 else 0
                matches.append({
                    'outcome_type': 'moneyline',
                    'predicted': predicted_value,
                    'actual': actual_winner,
                    'error': abs(predicted_value - actual_winner)
                })
            
            elif outcome_type == 'spread':
                matches.append({
                    'outcome_type': 'spread',
                    'predicted': predicted_value,
                    'actual': point_diff,
                    'error': abs(predicted_value - point_diff)
                })
            
            elif outcome_type == 'total':
                matches.append({
                    'outcome_type': 'total',
                    'predicted': predicted_value,
                    'actual': total_score,
                    'error': abs(predicted_value - total_score)
                })
        
        return matches
    
    def run_validation(self, date):
        """Run complete validation for a specific date"""
        print(f"📈 RUNNING PREDICTION VALIDATION FOR {date.strftime('%Y-%m-%d')}")
        print("=" * 60)
        
        # Load data
        print("📊 Loading predictions...")
        predictions = self.load_predictions(date)
        print(f"   ✅ Loaded {len(predictions)} prediction sets")
        
        print("📊 Loading boxscores...")
        boxscores = self.load_boxscores(date)
        print(f"   ✅ Loaded {len(boxscores)} boxscores")
        
        if not predictions or not boxscores:
            print("❌ Insufficient data for validation")
            return None
        
        # Match predictions to actuals
        print("🔗 Matching predictions to actuals...")
        matches = self.match_predictions_to_actuals(predictions, boxscores)
        
        print(f"   ✅ Player prop matches: {len(matches['player_props'])}")
        print(f"   ✅ Game outcome matches: {len(matches['game_outcomes'])}")
        print(f"   ⚠️ Unmatched predictions: {len(matches['unmatched_predictions'])}")
        print(f"   ⚠️ Unmatched boxscores: {len(matches['unmatched_boxscores'])}")
        
        # Calculate metrics
        print("📊 Calculating validation metrics...")
        validation_results = self.calculate_validation_metrics(matches)
        
        # Generate reports
        print("📄 Generating validation report...")
        report_path = self.generate_validation_report(date, validation_results, matches)
        
        print(f"✅ Validation complete! Report saved to: {report_path}")
        return validation_results
    
    def calculate_validation_metrics(self, matches):
        """Calculate comprehensive validation metrics"""
        results = {
            'player_props': {},
            'game_outcomes': {},
            'overall': {},
            'insights': []
        }
        
        # Player props metrics
        if matches['player_props']:
            props_df = pd.DataFrame(matches['player_props'])
            
            for stat_type in props_df['stat_type'].unique():
                stat_data = props_df[props_df['stat_type'] == stat_type]
                predictions = stat_data['predicted'].values
                actuals = stat_data['actual'].values
                
                metrics = self.metrics_calculator.calculate_player_prop_accuracy(
                    predictions, actuals, stat_type
                )
                results['player_props'][stat_type] = metrics
        
        # Game outcomes metrics
        if matches['game_outcomes']:
            outcomes_df = pd.DataFrame(matches['game_outcomes'])
            
            for outcome_type in outcomes_df['outcome_type'].unique():
                outcome_data = outcomes_df[outcomes_df['outcome_type'] == outcome_type]
                predictions = outcome_data['predicted'].values
                actuals = outcome_data['actual'].values
                
                metrics = self.metrics_calculator.calculate_game_outcome_accuracy(
                    predictions, actuals, outcome_type
                )
                results['game_outcomes'][outcome_type] = metrics
        
        # Overall metrics
        results['overall'] = self._calculate_overall_metrics(matches)
        
        # Generate insights
        results['insights'] = self._generate_insights(matches, results)
        
        return results
    
    def _calculate_overall_metrics(self, matches):
        """Calculate overall performance metrics"""
        overall = {
            'total_predictions': len(matches['player_props']) + len(matches['game_outcomes']),
            'player_prop_count': len(matches['player_props']),
            'game_outcome_count': len(matches['game_outcomes']),
            'avg_player_error': 0,
            'avg_game_error': 0,
            'best_predictions': [],
            'worst_predictions': []
        }
        
        if matches['player_props']:
            props_df = pd.DataFrame(matches['player_props'])
            overall['avg_player_error'] = round(props_df['error'].mean(), 2)
            
            # Best and worst predictions
            best_idx = props_df['error'].idxmin()
            worst_idx = props_df['error'].idxmax()
            
            overall['best_predictions'].append({
                'type': 'player_prop',
                'details': props_df.iloc[best_idx].to_dict()
            })
            
            overall['worst_predictions'].append({
                'type': 'player_prop', 
                'details': props_df.iloc[worst_idx].to_dict()
            })
        
        if matches['game_outcomes']:
            outcomes_df = pd.DataFrame(matches['game_outcomes'])
            overall['avg_game_error'] = round(outcomes_df['error'].mean(), 2)
        
        return overall
    
    def _generate_insights(self, matches, results):
        """Generate actionable insights from validation results"""
        insights = []
        
        # Player prop insights
        if results['player_props']:
            for stat_type, metrics in results['player_props'].items():
                if metrics['accuracy'] < 60:
                    insights.append({
                        'type': 'warning',
                        'category': 'player_props',
                        'message': f"{stat_type.title()} model accuracy ({metrics['accuracy']}%) below 60% threshold",
                        'recommendation': f"Consider retraining {stat_type} model with recent data"
                    })
                elif metrics['accuracy'] > 80:
                    insights.append({
                        'type': 'success',
                        'category': 'player_props',
                        'message': f"{stat_type.title()} model performing excellently ({metrics['accuracy']}%)",
                        'recommendation': "Maintain current model parameters"
                    })
        
        # Game outcome insights
        if results['game_outcomes']:
            for outcome_type, metrics in results['game_outcomes'].items():
                if metrics is None:
                    continue
                accuracy_key = 'accuracy' if 'accuracy' in metrics else 'cover_accuracy'
                if accuracy_key in metrics and metrics[accuracy_key] < 55:
                    insights.append({
                        'type': 'warning',
                        'category': 'game_outcomes',
                        'message': f"{outcome_type.title()} predictions below 55% accuracy",
                        'recommendation': f"Review {outcome_type} model features and training data"
                    })
        
        return insights
    
    def generate_validation_report(self, date, results, matches):
        """Generate comprehensive validation report"""
        date_str = date.strftime('%Y-%m-%d')
        report_path = self.output_dir / f'{date_str}_validation_report.html'
        
        # Create HTML report
        html_content = self._create_html_report(date, results, matches)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # Also save JSON summary
        json_path = self.output_dir / f'{date_str}_validation_summary.json'
        with open(json_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        return report_path
    
    def _create_html_report(self, date, results, matches):
        """Create HTML validation report"""
        date_str = date.strftime('%Y-%m-%d')
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>HYPER_MEDUSA_NEURAL_VAULT Validation Report - {date_str}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; }}
                .metric-card {{ background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }}
                .success {{ border-left-color: #28a745; }}
                .warning {{ border-left-color: #ffc107; }}
                .error {{ border-left-color: #dc3545; }}
                .grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏀 HYPER_MEDUSA_NEURAL_VAULT</h1>
                <h2>Prediction Validation Report - {date_str}</h2>
                <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="grid">
                <div class="metric-card">
                    <h3>📊 Overall Performance</h3>
                    <p><strong>Total Predictions:</strong> {results['overall']['total_predictions']}</p>
                    <p><strong>Player Props:</strong> {results['overall']['player_prop_count']}</p>
                    <p><strong>Game Outcomes:</strong> {results['overall']['game_outcome_count']}</p>
                    <p><strong>Avg Player Error:</strong> {results['overall']['avg_player_error']}</p>
                    <p><strong>Avg Game Error:</strong> {results['overall']['avg_game_error']}</p>
                </div>
                
                <div class="metric-card">
                    <h3>🎯 Player Props Accuracy</h3>
                    {self._format_player_props_html(results.get('player_props', {}))}
                </div>
                
                <div class="metric-card">
                    <h3>🏀 Game Outcomes Accuracy</h3>
                    {self._format_game_outcomes_html(results.get('game_outcomes', {}))}
                </div>
            </div>
            
            <div class="metric-card">
                <h3>💡 Key Insights</h3>
                {self._format_insights_html(results.get('insights', []))}
            </div>
            
            <div class="metric-card">
                <h3>📈 Best Predictions</h3>
                {self._format_best_predictions_html(results['overall'].get('best_predictions', []))}
            </div>
            
            <div class="metric-card">
                <h3>📉 Worst Predictions</h3>
                {self._format_worst_predictions_html(results['overall'].get('worst_predictions', []))}
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _format_player_props_html(self, player_props):
        """Format player props metrics for HTML"""
        if not player_props:
            return "<p>No player prop data available</p>"
        
        html = "<table><tr><th>Stat</th><th>MAE</th><th>Accuracy</th><th>Within Threshold</th></tr>"
        for stat_type, metrics in player_props.items():
            threshold_key = [k for k in metrics.keys() if k.startswith('within_')]
            threshold_val = metrics[threshold_key[0]] if threshold_key else 'N/A'
            
            html += f"""
            <tr>
                <td>{stat_type.title()}</td>
                <td>{metrics['mae']}</td>
                <td>{metrics['accuracy']}%</td>
                <td>{threshold_val}%</td>
            </tr>
            """
        html += "</table>"
        return html
    
    def _format_game_outcomes_html(self, game_outcomes):
        """Format game outcomes metrics for HTML"""
        if not game_outcomes:
            return "<p>No game outcome data available</p>"
        
        html = "<table><tr><th>Outcome</th><th>Accuracy</th><th>MAE</th></tr>"
        for outcome_type, metrics in game_outcomes.items():
            accuracy = metrics.get('accuracy', metrics.get('cover_accuracy', metrics.get('over_accuracy', 'N/A')))
            mae = metrics.get('mae', 'N/A')
            
            html += f"""
            <tr>
                <td>{outcome_type.title()}</td>
                <td>{accuracy}%</td>
                <td>{mae}</td>
            </tr>
            """
        html += "</table>"
        return html
    
    def _format_insights_html(self, insights):
        """Format insights for HTML"""
        if not insights:
            return "<p>No specific insights generated</p>"
        
        html = ""
        for insight in insights:
            css_class = insight['type']
            html += f"""
            <div class="metric-card {css_class}">
                <strong>{insight['message']}</strong><br>
                <em>Recommendation: {insight['recommendation']}</em>
            </div>
            """
        return html
    
    def _format_best_predictions_html(self, best_predictions):
        """Format best predictions for HTML"""
        if not best_predictions:
            return "<p>No best predictions data available</p>"
        
        html = ""
        for pred in best_predictions:
            details = pred['details']
            html += f"""
            <p><strong>{details['player']} {details['stat_type']}:</strong> 
            Predicted {details['predicted']}, Actual {details['actual']} 
            (Error: {details['error']})</p>
            """
        return html
    
    def _format_worst_predictions_html(self, worst_predictions):
        """Format worst predictions for HTML"""
        if not worst_predictions:
            return "<p>No worst predictions data available</p>"
        
        html = ""
        for pred in worst_predictions:
            details = pred['details']
            html += f"""
            <p><strong>{details['player']} {details['stat_type']}:</strong> 
            Predicted {details['predicted']}, Actual {details['actual']} 
            (Error: {details['error']})</p>
            """
        return html

def main():
    """Main validation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="📈 Prediction Validation System")
    parser.add_argument("--date", help="Date to validate (YYYY-MM-DD)")
    parser.add_argument("--days-back", type=int, default=1, help="Days back from today")
    parser.add_argument("--prediction-dir", default="predictions/daily", help="Predictions directory")
    parser.add_argument("--boxscore-dir", default="data/boxscores", help="Boxscores directory")
    parser.add_argument("--output-dir", default="reports/validation", help="Output directory")
    
    args = parser.parse_args()
    
    # Determine validation date using DateManager
    date_manager = DateManager()
    if args.date:
        validation_date = datetime.strptime(args.date, '%Y-%m-%d')
    else:
        # Use DateManager to get the correct validation date
        validation_date = datetime.combine(date_manager.get_validation_date(), datetime.min.time())
    
    # Create validator and run
    validator = PredictionValidator(
        prediction_dir=args.prediction_dir,
        boxscore_dir=args.boxscore_dir,
        output_dir=args.output_dir
    )
    
    results = validator.run_validation(validation_date)
    
    if results:
        print("\n🎯 VALIDATION SUMMARY:")
        print(f"   📊 Total Predictions: {results['overall']['total_predictions']}")
        print(f"   👥 Player Props: {results['overall']['player_prop_count']}")
        print(f"   🏀 Game Outcomes: {results['overall']['game_outcome_count']}")
        print(f"   📈 Avg Player Error: {results['overall']['avg_player_error']}")
        print(f"   📈 Avg Game Error: {results['overall']['avg_game_error']}")
        print(f"   💡 Insights Generated: {len(results['insights'])}")

if __name__ == "__main__":
    main()
