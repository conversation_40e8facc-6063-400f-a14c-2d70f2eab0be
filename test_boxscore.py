#!/usr/bin/env python3
"""
🧪 BOXSCORE TESTING UTILITY
Test individual boxscore retrieval and validation
"""

import argparse
import pandas as pd
import json
from datetime import datetime
from pathlib import Path
from boxscore_service import BoxscoreService

def test_single_game(game_id, output_file=None):
    """Test retrieval of a single game"""
    print(f"🧪 TESTING SINGLE GAME RETRIEVAL")
    print("=" * 40)
    print(f"🎯 Game ID: {game_id}")
    
    service = BoxscoreService()
    
    try:
        # Fetch boxscore
        print(f"📊 Fetching boxscore...")
        player_stats, team_stats = service.fetch_boxscore(game_id)
        
        print(f"✅ Retrieval successful!")
        print(f"   👥 Player records: {len(player_stats)}")
        print(f"   🏀 Team records: {len(team_stats)}")
        
        # Display sample data
        print(f"\n📋 PLAYER STATS SAMPLE:")
        if not player_stats.empty:
            print(player_stats[['PLAYER_NAME', 'TEAM_ABBREVIATION', 'PTS', 'REB', 'AST']].head())
        
        print(f"\n🏀 TEAM STATS SAMPLE:")
        if not team_stats.empty:
            print(team_stats[['TEAM_NAME', 'PTS', 'FG_PCT', 'REB', 'AST']].head())
        
        # Save if requested
        if output_file:
            output_path = Path(output_file)
            
            # Save as parquet
            player_file = output_path.with_suffix('.players.parquet')
            team_file = output_path.with_suffix('.teams.parquet')
            
            player_stats.to_parquet(player_file, index=False)
            team_stats.to_parquet(team_file, index=False)
            
            print(f"\n💾 Saved to:")
            print(f"   👥 Players: {player_file}")
            print(f"   🏀 Teams: {team_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_service_functionality():
    """Test all service functionality"""
    print(f"🧪 COMPREHENSIVE SERVICE TEST")
    print("=" * 40)
    
    service = BoxscoreService()
    test_results = {}
    
    # Test 1: WNBA Teams
    print(f"🏀 Test 1: WNBA Teams Retrieval")
    try:
        teams = service.wnba_teams
        print(f"   ✅ Found {len(teams)} WNBA teams")
        test_results['teams'] = True
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        test_results['teams'] = False
    
    # Test 2: Games Retrieval
    print(f"\n📅 Test 2: Games Retrieval")
    try:
        games = service.get_todays_games()
        print(f"   ✅ Found {len(games)} games")
        test_results['games'] = True
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        test_results['games'] = False
    
    # Test 3: Boxscore Retrieval
    print(f"\n📊 Test 3: Boxscore Retrieval")
    try:
        # Use simulated game ID
        player_stats, team_stats = service.fetch_boxscore('0012300001')
        print(f"   ✅ Retrieved {len(player_stats)} player records, {len(team_stats)} team records")
        test_results['boxscore'] = True
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        test_results['boxscore'] = False
    
    # Test 4: Data Saving
    print(f"\n💾 Test 4: Data Saving")
    try:
        # Create test data
        test_players = pd.DataFrame({
            'PLAYER_NAME': ['Test Player 1', 'Test Player 2'],
            'PTS': [20, 15],
            'REB': [8, 6],
            'AST': [5, 3]
        })
        
        test_teams = pd.DataFrame({
            'TEAM_NAME': ['Test Team A', 'Test Team B'],
            'PTS': [85, 82],
            'FG_PCT': [0.45, 0.42]
        })
        
        success = service.save_boxscore('TEST_GAME_001', test_players, test_teams)
        if success:
            print(f"   ✅ Data saved successfully")
            test_results['saving'] = True
        else:
            print(f"   ❌ Data saving failed")
            test_results['saving'] = False
            
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        test_results['saving'] = False
    
    # Test 5: File Cleanup
    print(f"\n🧹 Test 5: Cleanup Functionality")
    try:
        service.cleanup_old_files()
        print(f"   ✅ Cleanup completed")
        test_results['cleanup'] = True
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        test_results['cleanup'] = False
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 40)
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name.capitalize()}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    return passed == total

def validate_boxscore_data(file_path):
    """Validate boxscore data structure and content"""
    print(f"🔍 VALIDATING BOXSCORE DATA")
    print("=" * 40)
    print(f"📁 File: {file_path}")
    
    try:
        # Load data
        if str(file_path).endswith('.parquet'):
            df = pd.read_parquet(file_path)
        else:
            df = pd.read_csv(file_path)
        
        print(f"✅ File loaded successfully")
        print(f"   📊 Records: {len(df)}")
        print(f"   📋 Columns: {len(df.columns)}")
        
        # Basic validation
        validation_results = {}
        
        # Check for required columns (player stats)
        required_player_cols = ['PLAYER_NAME', 'PTS', 'REB', 'AST']
        if all(col in df.columns for col in required_player_cols):
            print(f"   ✅ Player stats format detected")
            validation_results['format'] = 'player'
            
            # Validate data ranges
            if df['PTS'].min() >= 0 and df['PTS'].max() <= 50:
                print(f"   ✅ Points values are realistic (0-50)")
                validation_results['points_range'] = True
            else:
                print(f"   ⚠️ Points values may be unrealistic")
                validation_results['points_range'] = False
            
            if df['REB'].min() >= 0 and df['REB'].max() <= 25:
                print(f"   ✅ Rebounds values are realistic (0-25)")
                validation_results['rebounds_range'] = True
            else:
                print(f"   ⚠️ Rebounds values may be unrealistic")
                validation_results['rebounds_range'] = False
        
        # Check for team stats
        required_team_cols = ['TEAM_NAME', 'PTS']
        if all(col in df.columns for col in required_team_cols):
            print(f"   ✅ Team stats format detected")
            validation_results['format'] = 'team'
            
            if df['PTS'].min() >= 50 and df['PTS'].max() <= 150:
                print(f"   ✅ Team points are realistic (50-150)")
                validation_results['team_points_range'] = True
            else:
                print(f"   ⚠️ Team points may be unrealistic")
                validation_results['team_points_range'] = False
        
        # Check for missing values
        missing_values = df.isnull().sum().sum()
        if missing_values == 0:
            print(f"   ✅ No missing values")
            validation_results['completeness'] = True
        else:
            print(f"   ⚠️ {missing_values} missing values found")
            validation_results['completeness'] = False
        
        # Check for duplicates
        duplicates = df.duplicated().sum()
        if duplicates == 0:
            print(f"   ✅ No duplicate records")
            validation_results['uniqueness'] = True
        else:
            print(f"   ⚠️ {duplicates} duplicate records found")
            validation_results['uniqueness'] = False
        
        # Display sample data
        print(f"\n📋 SAMPLE DATA:")
        print(df.head())
        
        # Summary
        passed_validations = sum(1 for v in validation_results.values() if v is True)
        total_validations = len([v for v in validation_results.values() if isinstance(v, bool)])
        
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"   ✅ Passed: {passed_validations}/{total_validations}")
        
        return validation_results
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return {}

def benchmark_performance():
    """Benchmark boxscore retrieval performance"""
    print(f"⚡ PERFORMANCE BENCHMARK")
    print("=" * 40)
    
    service = BoxscoreService()
    
    # Test multiple game retrievals
    test_games = ['0012300001', '0012300002', '0012300003']
    
    total_time = 0
    successful_retrievals = 0
    
    for i, game_id in enumerate(test_games, 1):
        print(f"🎯 Test {i}/{len(test_games)}: Game {game_id}")
        
        start_time = datetime.now()
        
        try:
            player_stats, team_stats = service.fetch_boxscore(game_id)
            end_time = datetime.now()
            
            retrieval_time = (end_time - start_time).total_seconds()
            total_time += retrieval_time
            successful_retrievals += 1
            
            print(f"   ✅ Success in {retrieval_time:.2f}s ({len(player_stats)} players, {len(team_stats)} teams)")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    if successful_retrievals > 0:
        avg_time = total_time / successful_retrievals
        print(f"\n📊 PERFORMANCE SUMMARY:")
        print(f"   ✅ Successful: {successful_retrievals}/{len(test_games)}")
        print(f"   ⏱️ Average time: {avg_time:.2f}s per game")
        print(f"   🚀 Throughput: {3600/avg_time:.1f} games per hour")
    else:
        print(f"\n❌ No successful retrievals")

def main():
    parser = argparse.ArgumentParser(description="🧪 Boxscore Testing Utility")
    parser.add_argument("--game-id", help="Test specific game ID")
    parser.add_argument("--output", help="Output file for test data")
    parser.add_argument("--validate", help="Validate boxscore file")
    parser.add_argument("--service-test", action="store_true", help="Test all service functionality")
    parser.add_argument("--benchmark", action="store_true", help="Run performance benchmark")
    
    args = parser.parse_args()
    
    if args.game_id:
        test_single_game(args.game_id, args.output)
    elif args.validate:
        validate_boxscore_data(args.validate)
    elif args.service_test:
        test_service_functionality()
    elif args.benchmark:
        benchmark_performance()
    else:
        print("🧪 Boxscore Testing Utility")
        print("Use --game-id, --validate, --service-test, or --benchmark")

if __name__ == "__main__":
    main()
