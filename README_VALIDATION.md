# HYPER_MEDUSA_NEURAL_VAULT Prediction Validation System

## 📈 Overview
This system automatically validates predictions against actual boxscore results and generates comprehensive performance reports.

## 🚀 Quick Start

### 1. Test the System
```bash
python test_validation.py --date 2025-07-07 --output test_reports
```

### 2. Run Daily Validation
```bash
python prediction_analyzer.py --date 2025-07-07
```

### 3. Schedule Automated Reports
```bash
python schedule_validation.py --time "03:00" --email-report
```

## 📊 Directory Structure
```
predictions/daily/     # Daily prediction files
data/boxscores/       # Boxscore data files
reports/validation/   # Generated validation reports
config/              # Configuration files
logs/                # System logs
```

## 📄 Prediction File Format
Predictions should be saved as JSON files in `predictions/daily/` with the format:
```json
{
  "game_id": {
    "game_info": {...},
    "player_props": {...},
    "game_outcomes": {...}
  }
}
```

## 📧 Email Configuration
Edit `config/validation_config.json` to enable email reports:
```json
{
  "email": {
    "enabled": true,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "recipients": ["<EMAIL>"]
  }
}
```

## 🔧 Commands

### Validation
- `python prediction_analyzer.py --date YYYY-MM-DD`
- `python prediction_analyzer.py --days-back 3`

### Testing
- `python test_validation.py`
- `python test_validation.py --cleanup`

### Scheduling
- `python schedule_validation.py --run-now`
- `python schedule_validation.py --time "03:00"`

## 📈 Metrics Tracked
- **Player Props**: Points, Rebounds, Assists, Steals, Blocks, Threes
- **Game Outcomes**: Moneyline, Spread, Totals
- **Accuracy**: Exact predictions and within-threshold accuracy
- **Error Analysis**: MAE, bias detection, trend analysis

## 🎯 Performance Thresholds
- **Player Props**: >70% accuracy target
- **Game Outcomes**: >55% accuracy target
- **MAE Thresholds**: Points <3.0, Rebounds <2.0, Assists <2.0

## 📊 Reports Generated
- Daily HTML validation reports
- JSON summary files
- Email notifications
- Performance trend analysis
- Model improvement recommendations
