#!/usr/bin/env python3
"""
🚀 PREDICTION VALIDATION SYSTEM INITIALIZATION
Setup and configure the complete validation system
"""

import os
import json
from pathlib import Path
import shutil

def create_directory_structure():
    """Create required directory structure"""
    directories = [
        'predictions/daily',
        'data/boxscores',
        'reports/validation',
        'config',
        'logs',
        'test_reports'
    ]
    
    print("📁 Creating directory structure...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Created: {directory}")

def create_config_files():
    """Create configuration files"""
    print("⚙️ Creating configuration files...")
    
    # Validation configuration
    validation_config = {
        "email": {
            "enabled": False,
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "recipients": []
        },
        "validation": {
            "retroactive_days": 7,
            "auto_update_models": False,
            "notification_thresholds": {
                "accuracy_drop": 5.0,
                "error_increase": 2.0
            }
        },
        "schedule": {
            "daily_time": "03:00",
            "timezone": "UTC"
        },
        "prediction_dir": "predictions/daily",
        "boxscore_dir": "data/boxscores",
        "output_dir": "reports/validation"
    }
    
    config_path = Path('config/validation_config.json')
    with open(config_path, 'w') as f:
        json.dump(validation_config, f, indent=2)
    print(f"   ✅ Created: {config_path}")
    
    # Email template configuration
    email_config = {
        "templates": {
            "daily_report": {
                "subject": "🏀 HYPER_MEDUSA_NEURAL_VAULT Daily Report - {date}",
                "include_attachments": True,
                "include_charts": True
            },
            "alert": {
                "subject": "⚠️ HYPER_MEDUSA_NEURAL_VAULT Alert - {alert_type}",
                "urgent_threshold": 10.0
            }
        },
        "formatting": {
            "date_format": "%Y-%m-%d",
            "time_format": "%H:%M:%S",
            "decimal_places": 2
        }
    }
    
    email_config_path = Path('config/email_config.json')
    with open(email_config_path, 'w') as f:
        json.dump(email_config, f, indent=2)
    print(f"   ✅ Created: {email_config_path}")

def create_sample_files():
    """Create sample files for testing"""
    print("📄 Creating sample files...")
    
    # Sample prediction format
    sample_prediction = {
        "game_example_001": {
            "game_info": {
                "game_id": "example_001",
                "date": "2025-07-07",
                "home_team": "Las Vegas Aces",
                "away_team": "Atlanta Dream"
            },
            "player_props": {
                "A'ja Wilson": {
                    "points": 24.5,
                    "rebounds": 11.2,
                    "assists": 3.1,
                    "steals": 1.0,
                    "blocks": 1.8,
                    "threes": 0.3
                },
                "Kelsey Plum": {
                    "points": 18.3,
                    "rebounds": 3.2,
                    "assists": 5.7,
                    "steals": 1.2,
                    "blocks": 0.1,
                    "threes": 2.4
                }
            },
            "game_outcomes": {
                "moneyline": 0.72,
                "spread": -8.5,
                "total": 162.3
            }
        }
    }
    
    sample_path = Path('predictions/daily/sample_prediction_format.json')
    with open(sample_path, 'w') as f:
        json.dump(sample_prediction, f, indent=2)
    print(f"   ✅ Created: {sample_path}")
    
    # README file
    readme_content = """# HYPER_MEDUSA_NEURAL_VAULT Prediction Validation System

## 📈 Overview
This system automatically validates predictions against actual boxscore results and generates comprehensive performance reports.

## 🚀 Quick Start

### 1. Test the System
```bash
python test_validation.py --date 2025-07-07 --output test_reports
```

### 2. Run Daily Validation
```bash
python prediction_analyzer.py --date 2025-07-07
```

### 3. Schedule Automated Reports
```bash
python schedule_validation.py --time "03:00" --email-report
```

## 📊 Directory Structure
```
predictions/daily/     # Daily prediction files
data/boxscores/       # Boxscore data files
reports/validation/   # Generated validation reports
config/              # Configuration files
logs/                # System logs
```

## 📄 Prediction File Format
Predictions should be saved as JSON files in `predictions/daily/` with the format:
```json
{
  "game_id": {
    "game_info": {...},
    "player_props": {...},
    "game_outcomes": {...}
  }
}
```

## 📧 Email Configuration
Edit `config/validation_config.json` to enable email reports:
```json
{
  "email": {
    "enabled": true,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "recipients": ["<EMAIL>"]
  }
}
```

## 🔧 Commands

### Validation
- `python prediction_analyzer.py --date YYYY-MM-DD`
- `python prediction_analyzer.py --days-back 3`

### Testing
- `python test_validation.py`
- `python test_validation.py --cleanup`

### Scheduling
- `python schedule_validation.py --run-now`
- `python schedule_validation.py --time "03:00"`

## 📈 Metrics Tracked
- **Player Props**: Points, Rebounds, Assists, Steals, Blocks, Threes
- **Game Outcomes**: Moneyline, Spread, Totals
- **Accuracy**: Exact predictions and within-threshold accuracy
- **Error Analysis**: MAE, bias detection, trend analysis

## 🎯 Performance Thresholds
- **Player Props**: >70% accuracy target
- **Game Outcomes**: >55% accuracy target
- **MAE Thresholds**: Points <3.0, Rebounds <2.0, Assists <2.0

## 📊 Reports Generated
- Daily HTML validation reports
- JSON summary files
- Email notifications
- Performance trend analysis
- Model improvement recommendations
"""
    
    readme_path = Path('README_VALIDATION.md')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"   ✅ Created: {readme_path}")

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    requirements = [
        'pandas>=1.5.0',
        'numpy>=1.21.0',
        'scikit-learn>=1.0.0',
        'plotly>=5.0.0',
        'schedule>=1.1.0',
        'pyarrow>=5.0.0'
    ]
    
    requirements_path = Path('requirements_validation.txt')
    with open(requirements_path, 'w') as f:
        f.write('\n'.join(requirements))
    print(f"   ✅ Created: {requirements_path}")
    
    print("   💡 To install dependencies, run:")
    print("      pip install -r requirements_validation.txt")

def create_startup_scripts():
    """Create startup scripts"""
    print("🚀 Creating startup scripts...")
    
    # Windows batch script
    windows_script = """@echo off
echo 🏀 HYPER_MEDUSA_NEURAL_VAULT Validation System
echo ===============================================

echo 📊 Running daily validation...
python prediction_analyzer.py --days-back 1

echo 📧 Generating email report...
python schedule_validation.py --run-now --email-report

echo ✅ Validation complete!
pause
"""
    
    windows_path = Path('run_validation.bat')
    with open(windows_path, 'w', encoding='utf-8') as f:
        f.write(windows_script)
    print(f"   ✅ Created: {windows_path}")

    # Unix shell script
    unix_script = """#!/bin/bash
echo "🏀 HYPER_MEDUSA_NEURAL_VAULT Validation System"
echo "==============================================="

echo "📊 Running daily validation..."
python prediction_analyzer.py --days-back 1

echo "📧 Generating email report..."
python schedule_validation.py --run-now --email-report

echo "✅ Validation complete!"
"""

    unix_path = Path('run_validation.sh')
    with open(unix_path, 'w', encoding='utf-8') as f:
        f.write(unix_script)
    
    # Make executable on Unix systems
    try:
        os.chmod(unix_path, 0o755)
    except:
        pass
    
    print(f"   ✅ Created: {unix_path}")

def run_system_check():
    """Run system check to verify installation"""
    print("🔍 Running system check...")
    
    checks = [
        ("Python imports", check_python_imports),
        ("Directory structure", check_directories),
        ("Configuration files", check_config_files),
        ("Sample data", check_sample_data)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            result = check_func()
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        except Exception as e:
            print(f"   ❌ FAIL {check_name}: {e}")
            all_passed = False
    
    return all_passed

def check_python_imports():
    """Check if required Python packages can be imported"""
    try:
        import pandas
        import numpy
        import sklearn
        import plotly
        return True
    except ImportError:
        return False

def check_directories():
    """Check if required directories exist"""
    required_dirs = ['predictions/daily', 'data/boxscores', 'reports/validation', 'config', 'logs']
    return all(Path(d).exists() for d in required_dirs)

def check_config_files():
    """Check if configuration files exist"""
    config_files = ['config/validation_config.json', 'config/email_config.json']
    return all(Path(f).exists() for f in config_files)

def check_sample_data():
    """Check if sample files exist"""
    sample_files = ['predictions/daily/sample_prediction_format.json', 'README_VALIDATION.md']
    return all(Path(f).exists() for f in sample_files)

def main():
    """Main initialization function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="🚀 Initialize Prediction Validation System")
    parser.add_argument("--prediction-path", default="predictions/daily", help="Predictions directory")
    parser.add_argument("--boxscore-path", default="data/boxscores", help="Boxscores directory")
    parser.add_argument("--output", default="reports/validation", help="Output directory")
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency installation")
    parser.add_argument("--test-run", action="store_true", help="Run test validation after setup")
    
    args = parser.parse_args()
    
    print("🚀 INITIALIZING PREDICTION VALIDATION SYSTEM")
    print("=" * 60)
    print()
    
    # Update paths in config if provided
    if args.prediction_path != "predictions/daily":
        print(f"📁 Using custom prediction path: {args.prediction_path}")
    if args.boxscore_path != "data/boxscores":
        print(f"📁 Using custom boxscore path: {args.boxscore_path}")
    if args.output != "reports/validation":
        print(f"📁 Using custom output path: {args.output}")
    
    # Run initialization steps
    create_directory_structure()
    print()
    
    create_config_files()
    print()
    
    create_sample_files()
    print()
    
    if not args.skip_deps:
        install_dependencies()
        print()
    
    create_startup_scripts()
    print()
    
    # Run system check
    print("🔍 SYSTEM CHECK")
    print("-" * 30)
    system_ok = run_system_check()
    print()
    
    if system_ok:
        print("✅ INITIALIZATION COMPLETE!")
        print()
        print("🎯 Next Steps:")
        print("   1. Install dependencies: pip install -r requirements_validation.txt")
        print("   2. Configure email (optional): edit config/validation_config.json")
        print("   3. Test the system: python test_validation.py")
        print("   4. Run validation: python prediction_analyzer.py")
        print("   5. Schedule daily reports: python schedule_validation.py")
        print()
        
        if args.test_run:
            print("🧪 Running test validation...")
            os.system("python test_validation.py --cleanup")
        
    else:
        print("❌ INITIALIZATION INCOMPLETE!")
        print("   Please check the failed items above and retry.")

if __name__ == "__main__":
    main()
