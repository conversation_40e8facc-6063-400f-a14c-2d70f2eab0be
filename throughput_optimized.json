{"timestamp": "2025-07-07T18:22:35.537116", "configuration": {"batch_size": 64, "max_concurrency": 1000, "device": "cuda"}, "baseline_performance": {"duration_seconds": 60.00022315979004, "total_predictions": 396635, "predictions_per_second": 6610.558746484969, "predictions_per_minute": 396633.5247890981, "avg_latency_ms": 0.022066078752033955, "p95_latency_ms": 0.06747245788574219, "p99_latency_ms": 0.12540817260742188}, "optimized_performance": {"duration_seconds": 60.05228567123413, "total_predictions": 18508800, "predictions_per_second": 308211.4159872181, "predictions_per_minute": 18492684.959233087, "avg_latency_ms": 28.732701375111834, "p95_latency_ms": 68.06923151016235, "p99_latency_ms": 107.37833023071298, "batch_size": 64, "max_concurrency": 1000}, "optimization_gains": {"throughput_improvement": {"baseline_rpm": 396633.5247890981, "optimized_rpm": 18492684.959233087, "improvement_factor": 46.624109671682945, "improvement_percent": 4562.4109671682945}, "latency_improvement": {"baseline_ms": 0.022066078752033955, "optimized_ms": 28.732701375111834, "improvement_factor": 0.0007679778682817313, "improvement_percent": -99.92320221317182}, "efficiency_gains": {"batch_efficiency": 64, "concurrency_factor": 1000, "overall_efficiency": 0.0728501713620046}}, "recommendations": [{"priority": "HIGH", "action": "Deploy optimized configuration immediately", "reason": "46.6x throughput improvement achieved"}, {"priority": "MEDIUM", "action": "Test larger batch sizes", "reason": "Potential for additional throughput gains"}]}