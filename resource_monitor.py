#!/usr/bin/env python3
"""
📊 RESOURCE MONITORING DASHBOARD
Real-time monitoring for HYPER_MEDUSA_NEURAL_VAULT
"""

import time
import psutil
import json
import argparse
from datetime import datetime
from pathlib import Path
import subprocess

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

class ResourceMonitor:
    def __init__(self, refresh_rate=5):
        self.refresh_rate = refresh_rate
        self.history = []
        self.max_history = 1000
        
    def get_cpu_info(self):
        """Get CPU information"""
        return {
            "usage_percent": psutil.cpu_percent(interval=1),
            "cores": psutil.cpu_count(),
            "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    
    def get_memory_info(self):
        """Get memory information"""
        memory = psutil.virtual_memory()
        return {
            "total_gb": memory.total / 1024**3,
            "available_gb": memory.available / 1024**3,
            "used_gb": memory.used / 1024**3,
            "usage_percent": memory.percent,
            "free_gb": memory.free / 1024**3
        }
    
    def get_gpu_info(self):
        """Get GPU information"""
        if not TORCH_AVAILABLE:
            return {"available": False, "error": "PyTorch not available"}
        
        if not torch.cuda.is_available():
            return {"available": False, "error": "CUDA not available"}
        
        try:
            gpu_id = 0
            gpu_props = torch.cuda.get_device_properties(gpu_id)
            memory_allocated = torch.cuda.memory_allocated(gpu_id) / 1024**3
            memory_reserved = torch.cuda.memory_reserved(gpu_id) / 1024**3
            memory_total = gpu_props.total_memory / 1024**3
            
            # Try to get temperature (may not work on all systems)
            try:
                result = subprocess.run(['nvidia-smi', '--query-gpu=temperature.gpu', '--format=csv,noheader,nounits'], 
                                      capture_output=True, text=True, timeout=5)
                temperature = int(result.stdout.strip()) if result.returncode == 0 else None
            except:
                temperature = None
            
            return {
                "available": True,
                "name": gpu_props.name,
                "memory_allocated_gb": memory_allocated,
                "memory_reserved_gb": memory_reserved,
                "memory_total_gb": memory_total,
                "memory_usage_percent": (memory_reserved / memory_total) * 100,
                "compute_capability": f"{gpu_props.major}.{gpu_props.minor}",
                "multiprocessor_count": gpu_props.multi_processor_count,
                "temperature_c": temperature
            }
        except Exception as e:
            return {"available": False, "error": str(e)}
    
    def get_disk_info(self):
        """Get disk information"""
        disk = psutil.disk_usage('/')
        return {
            "total_gb": disk.total / 1024**3,
            "used_gb": disk.used / 1024**3,
            "free_gb": disk.free / 1024**3,
            "usage_percent": (disk.used / disk.total) * 100
        }
    
    def get_network_info(self):
        """Get network information"""
        net_io = psutil.net_io_counters()
        return {
            "bytes_sent": net_io.bytes_sent,
            "bytes_recv": net_io.bytes_recv,
            "packets_sent": net_io.packets_sent,
            "packets_recv": net_io.packets_recv
        }
    
    def get_process_info(self):
        """Get process information for local server"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = proc.cmdline()
                    if any('local_prediction_server' in arg for arg in cmdline):
                        processes.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cpu_percent": proc.info['cpu_percent'],
                            "memory_percent": proc.info['memory_percent'],
                            "cmdline": ' '.join(cmdline)
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return processes
    
    def collect_metrics(self):
        """Collect all metrics"""
        timestamp = datetime.now()
        
        metrics = {
            "timestamp": timestamp.isoformat(),
            "cpu": self.get_cpu_info(),
            "memory": self.get_memory_info(),
            "gpu": self.get_gpu_info(),
            "disk": self.get_disk_info(),
            "network": self.get_network_info(),
            "processes": self.get_process_info()
        }
        
        # Add to history
        self.history.append(metrics)
        if len(self.history) > self.max_history:
            self.history.pop(0)
        
        return metrics
    
    def generate_html_report(self, output_file="local_monitor.html"):
        """Generate HTML monitoring dashboard"""
        
        latest_metrics = self.collect_metrics()
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>🖥️ HYPER_MEDUSA_NEURAL_VAULT Resource Monitor</title>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="{self.refresh_rate}">
    <style>
        body {{ font-family: 'Segoe UI', Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
        .metric-card {{ background: #2d2d2d; border-radius: 10px; padding: 20px; border-left: 4px solid #00ff88; }}
        .metric-title {{ font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #00ff88; }}
        .metric-value {{ font-size: 24px; font-weight: bold; margin: 10px 0; }}
        .metric-detail {{ font-size: 14px; color: #ccc; margin: 5px 0; }}
        .status-good {{ color: #00ff88; }}
        .status-warning {{ color: #ffaa00; }}
        .status-critical {{ color: #ff4444; }}
        .progress-bar {{ width: 100%; height: 20px; background: #444; border-radius: 10px; overflow: hidden; }}
        .progress-fill {{ height: 100%; transition: width 0.3s ease; }}
        .timestamp {{ text-align: center; color: #888; margin-top: 20px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🖥️ HYPER_MEDUSA_NEURAL_VAULT</h1>
        <h2>📊 Real-Time Resource Monitor</h2>
    </div>
    
    <div class="metrics-grid">
        <!-- CPU Metrics -->
        <div class="metric-card">
            <div class="metric-title">🔥 CPU Performance</div>
            <div class="metric-value status-{'good' if latest_metrics['cpu']['usage_percent'] < 70 else 'warning' if latest_metrics['cpu']['usage_percent'] < 90 else 'critical'}">
                {latest_metrics['cpu']['usage_percent']:.1f}%
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {latest_metrics['cpu']['usage_percent']}%; background: {'#00ff88' if latest_metrics['cpu']['usage_percent'] < 70 else '#ffaa00' if latest_metrics['cpu']['usage_percent'] < 90 else '#ff4444'};"></div>
            </div>
            <div class="metric-detail">Cores: {latest_metrics['cpu']['cores']}</div>
            {f"<div class='metric-detail'>Frequency: {latest_metrics['cpu']['frequency']['current']:.0f} MHz</div>" if latest_metrics['cpu']['frequency'] else ""}
        </div>
        
        <!-- Memory Metrics -->
        <div class="metric-card">
            <div class="metric-title">💾 Memory Usage</div>
            <div class="metric-value status-{'good' if latest_metrics['memory']['usage_percent'] < 70 else 'warning' if latest_metrics['memory']['usage_percent'] < 90 else 'critical'}">
                {latest_metrics['memory']['usage_percent']:.1f}%
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {latest_metrics['memory']['usage_percent']}%; background: {'#00ff88' if latest_metrics['memory']['usage_percent'] < 70 else '#ffaa00' if latest_metrics['memory']['usage_percent'] < 90 else '#ff4444'};"></div>
            </div>
            <div class="metric-detail">Used: {latest_metrics['memory']['used_gb']:.1f}GB / {latest_metrics['memory']['total_gb']:.1f}GB</div>
            <div class="metric-detail">Available: {latest_metrics['memory']['available_gb']:.1f}GB</div>
        </div>
        
        <!-- GPU Metrics -->
        <div class="metric-card">
            <div class="metric-title">🖥️ GPU Status</div>
            {f'''
            <div class="metric-value status-good">{latest_metrics['gpu']['name']}</div>
            <div class="metric-detail">Memory: {latest_metrics['gpu']['memory_reserved_gb']:.2f}GB / {latest_metrics['gpu']['memory_total_gb']:.1f}GB</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {latest_metrics['gpu']['memory_usage_percent']}%; background: #00ff88;"></div>
            </div>
            <div class="metric-detail">Compute: {latest_metrics['gpu']['compute_capability']}</div>
            {f"<div class='metric-detail'>Temperature: {latest_metrics['gpu']['temperature_c']}°C</div>" if latest_metrics['gpu']['temperature_c'] else ""}
            ''' if latest_metrics['gpu']['available'] else f'''
            <div class="metric-value status-critical">Not Available</div>
            <div class="metric-detail">Error: {latest_metrics['gpu']['error']}</div>
            '''}
        </div>
        
        <!-- Disk Metrics -->
        <div class="metric-card">
            <div class="metric-title">💿 Disk Usage</div>
            <div class="metric-value status-{'good' if latest_metrics['disk']['usage_percent'] < 80 else 'warning' if latest_metrics['disk']['usage_percent'] < 95 else 'critical'}">
                {latest_metrics['disk']['usage_percent']:.1f}%
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {latest_metrics['disk']['usage_percent']}%; background: {'#00ff88' if latest_metrics['disk']['usage_percent'] < 80 else '#ffaa00' if latest_metrics['disk']['usage_percent'] < 95 else '#ff4444'};"></div>
            </div>
            <div class="metric-detail">Used: {latest_metrics['disk']['used_gb']:.1f}GB / {latest_metrics['disk']['total_gb']:.1f}GB</div>
            <div class="metric-detail">Free: {latest_metrics['disk']['free_gb']:.1f}GB</div>
        </div>
        
        <!-- Process Metrics -->
        <div class="metric-card">
            <div class="metric-title">⚙️ Server Processes</div>
            {f'''
            <div class="metric-value status-good">{len(latest_metrics['processes'])} Active</div>
            ''' + ''.join([f'''
            <div class="metric-detail">PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}% | RAM {proc['memory_percent']:.1f}%</div>
            ''' for proc in latest_metrics['processes'][:3]]) if latest_metrics['processes'] else '''
            <div class="metric-value status-warning">No Server Found</div>
            <div class="metric-detail">Local server not running</div>
            '''}
        </div>
        
        <!-- Network Metrics -->
        <div class="metric-card">
            <div class="metric-title">🌐 Network Activity</div>
            <div class="metric-value status-good">{latest_metrics['network']['bytes_recv'] / 1024**2:.1f}MB</div>
            <div class="metric-detail">Received: {latest_metrics['network']['bytes_recv'] / 1024**2:.1f}MB</div>
            <div class="metric-detail">Sent: {latest_metrics['network']['bytes_sent'] / 1024**2:.1f}MB</div>
            <div class="metric-detail">Packets: {latest_metrics['network']['packets_recv']:,}</div>
        </div>
    </div>
    
    <div class="timestamp">
        📅 Last Updated: {latest_metrics['timestamp']} | 🔄 Auto-refresh: {self.refresh_rate}s
    </div>
</body>
</html>
        """
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_file
    
    def run_continuous_monitoring(self, output_file="local_monitor.html"):
        """Run continuous monitoring"""
        print(f"📊 Starting resource monitoring (refresh: {self.refresh_rate}s)")
        print(f"🌐 Dashboard: file://{Path(output_file).absolute()}")
        print("Press Ctrl+C to stop...")
        
        try:
            while True:
                self.generate_html_report(output_file)
                metrics = self.history[-1] if self.history else {}
                
                # Print summary to console
                if metrics:
                    print(f"\r⚡ CPU: {metrics['cpu']['usage_percent']:.1f}% | "
                          f"💾 RAM: {metrics['memory']['usage_percent']:.1f}% | "
                          f"🖥️ GPU: {metrics['gpu']['memory_usage_percent']:.1f}% | "
                          f"📊 {datetime.now().strftime('%H:%M:%S')}", end="")
                
                time.sleep(self.refresh_rate)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped")

def main():
    parser = argparse.ArgumentParser(description="📊 Resource Monitor for HYPER_MEDUSA_NEURAL_VAULT")
    parser.add_argument("--refresh", type=int, default=5, help="Refresh rate in seconds")
    parser.add_argument("--output", default="local_monitor.html", help="Output HTML file")
    parser.add_argument("--gpu", action="store_true", help="Include GPU monitoring")
    parser.add_argument("--cpu", action="store_true", help="Include CPU monitoring")
    parser.add_argument("--network", action="store_true", help="Include network monitoring")
    parser.add_argument("--once", action="store_true", help="Generate report once and exit")
    
    args = parser.parse_args()
    
    monitor = ResourceMonitor(refresh_rate=args.refresh)
    
    if args.once:
        output_file = monitor.generate_html_report(args.output)
        print(f"📊 Report generated: {output_file}")
    else:
        monitor.run_continuous_monitoring(args.output)

if __name__ == "__main__":
    main()
