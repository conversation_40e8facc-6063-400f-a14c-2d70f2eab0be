#!/usr/bin/env python3
"""
🔍 MODEL DISCOVERY SYSTEM
Automatically discover and catalog all model files in the system
"""

import json
from pathlib import Path
import torch
from datetime import datetime

def discover_all_models():
    """Discover all model files in the system"""
    print("🔍 DISCOVERING ALL MODEL FILES")
    print("=" * 50)
    
    # Search directories
    search_dirs = [
        Path("models"),
        Path("checkpoints"),
        Path("checkpoints/game_totals"),
        Path("checkpoints/moneyline"),
        Path("checkpoints/player_points"),
        Path("checkpoints/rebounds"),
        Path("checkpoints/assists"),
        Path("checkpoints/threes"),
        Path("checkpoints/steals_blocks")
    ]
    
    discovered_models = {}
    
    for search_dir in search_dirs:
        if search_dir.exists():
            print(f"\n📁 Searching {search_dir}...")
            
            # Find all model files
            model_files = []
            model_files.extend(list(search_dir.glob("*.ckpt")))
            model_files.extend(list(search_dir.glob("*.pth")))
            model_files.extend(list(search_dir.glob("*.pt")))
            
            for model_file in model_files:
                print(f"   📄 Found: {model_file}")
                
                # Get file info
                file_size_mb = model_file.stat().st_size / (1024 * 1024)
                
                # Try to load and get metadata
                metadata = {}
                try:
                    if model_file.suffix == '.ckpt':
                        checkpoint = torch.load(model_file, map_location='cpu', weights_only=False)
                        if isinstance(checkpoint, dict):
                            if 'epoch' in checkpoint:
                                metadata['epoch'] = checkpoint['epoch']
                            if 'state_dict' in checkpoint:
                                metadata['has_state_dict'] = True
                                metadata['param_count'] = len(checkpoint['state_dict'])
                            if 'val_loss' in checkpoint:
                                metadata['val_loss'] = float(checkpoint['val_loss'])
                        metadata['loadable'] = True
                except Exception as e:
                    metadata['loadable'] = False
                    metadata['error'] = str(e)
                
                # Store discovery info
                discovered_models[str(model_file)] = {
                    'path': str(model_file),
                    'size_mb': round(file_size_mb, 1),
                    'type': model_file.suffix,
                    'directory': str(search_dir),
                    'metadata': metadata,
                    'discovered_at': datetime.now().isoformat()
                }
                
                print(f"      📊 Size: {file_size_mb:.1f}MB")
                if metadata.get('epoch'):
                    print(f"      📈 Epoch: {metadata['epoch']}")
                if metadata.get('val_loss'):
                    print(f"      📉 Val Loss: {metadata['val_loss']:.4f}")
                print(f"      ✅ Loadable: {'Yes' if metadata.get('loadable') else 'No'}")
    
    print(f"\n📊 DISCOVERY SUMMARY:")
    print(f"   📄 Total Files Found: {len(discovered_models)}")
    
    # Group by type
    ckpt_files = [f for f in discovered_models.values() if f['type'] == '.ckpt']
    pth_files = [f for f in discovered_models.values() if f['type'] == '.pth']
    
    print(f"   ⚡ PyTorch Lightning (.ckpt): {len(ckpt_files)}")
    print(f"   🔥 PyTorch (.pth): {len(pth_files)}")
    
    # Show loadable vs non-loadable
    loadable = [f for f in discovered_models.values() if f['metadata'].get('loadable')]
    print(f"   ✅ Loadable: {len(loadable)}")
    print(f"   ❌ Non-loadable: {len(discovered_models) - len(loadable)}")
    
    return discovered_models

def create_smart_registry(discovered_models):
    """Create a smart model registry based on discovered models"""
    print("\n🧠 CREATING SMART MODEL REGISTRY")
    print("=" * 40)
    
    # Smart mapping based on file names and paths
    smart_mapping = {
        'step1_points': {
            'keywords': ['points', 'player_points'],
            'preferred_dir': 'models',
            'description': 'Step 1: Player Points Model'
        },
        'step2_totals': {
            'keywords': ['totals', 'game_totals'],
            'preferred_dir': 'checkpoints/game_totals',
            'description': 'Step 2: Game Totals Model'
        },
        'step3_moneyline': {
            'keywords': ['moneyline'],
            'preferred_dir': 'models',
            'description': 'Step 3: Moneyline Model'
        },
        'step4_rebounds': {
            'keywords': ['rebounds', 'player_rebounds'],
            'preferred_dir': 'models',
            'description': 'Step 4: Player Rebounds Model'
        },
        'step5_assists': {
            'keywords': ['assists', 'player_assists'],
            'preferred_dir': 'models',
            'description': 'Step 5: Player Assists Model'
        },
        'step6_assists_v2': {
            'keywords': ['step6', 'assists'],
            'preferred_dir': 'models',
            'description': 'Step 6: Enhanced Assists Model'
        },
        'step7_threes': {
            'keywords': ['step7', 'threes'],
            'preferred_dir': 'models',
            'description': 'Step 7: Three-Pointers Model'
        },
        'step8_steals': {
            'keywords': ['step8', 'steals'],
            'preferred_dir': 'models',
            'description': 'Step 8: Steals Model'
        },
        'step8_blocks': {
            'keywords': ['step8', 'blocks'],
            'preferred_dir': 'models',
            'description': 'Step 8: Blocks Model'
        }
    }
    
    registry = {
        "models": {},
        "verification": {
            "check_file_exists": True,
            "check_file_size": True,
            "check_pytorch_load": True,
            "check_model_structure": True,
            "require_all_models": True
        },
        "discovery": {
            "last_discovery": datetime.now().isoformat(),
            "total_files_found": len(discovered_models),
            "auto_mapped": 0
        }
    }
    
    # Auto-map discovered models
    for model_key, mapping_info in smart_mapping.items():
        best_match = None
        best_score = 0
        
        for file_path, file_info in discovered_models.items():
            if not file_info['metadata'].get('loadable'):
                continue
                
            score = 0
            file_path_lower = file_path.lower()
            
            # Score based on keywords
            for keyword in mapping_info['keywords']:
                if keyword in file_path_lower:
                    score += 10
            
            # Bonus for preferred directory
            if mapping_info['preferred_dir'] in file_path:
                score += 5
            
            # Bonus for larger files (more likely to be complete models)
            if file_info['size_mb'] > 1:
                score += 2
            
            # Bonus for having validation loss (trained model)
            if file_info['metadata'].get('val_loss'):
                score += 3
            
            if score > best_score:
                best_score = score
                best_match = file_info
        
        if best_match:
            registry['models'][model_key] = {
                'path': best_match['path'],
                'type': 'pytorch_lightning' if best_match['type'] == '.ckpt' else 'pytorch',
                'description': mapping_info['description'],
                'required': True,
                'expected_size_mb': best_match['size_mb'],
                'last_trained': datetime.now().strftime('%Y-%m-%d'),
                'auto_mapped': True,
                'mapping_score': best_score,
                'metadata': best_match['metadata']
            }
            registry['discovery']['auto_mapped'] += 1
            print(f"   ✅ {model_key}: {best_match['path']} (score: {best_score})")
        else:
            print(f"   ❌ {model_key}: No suitable match found")
    
    return registry

def main():
    """Main discovery function"""
    # Discover all models
    discovered = discover_all_models()
    
    # Create smart registry
    registry = create_smart_registry(discovered)
    
    # Save discovery results
    discovery_file = Path('config/discovered_models.json')
    discovery_file.parent.mkdir(parents=True, exist_ok=True)
    with open(discovery_file, 'w') as f:
        json.dump(discovered, f, indent=2)
    
    # Save smart registry
    registry_file = Path('config/model_registry.json')
    with open(registry_file, 'w') as f:
        json.dump(registry, f, indent=2)
    
    print(f"\n💾 SAVED RESULTS:")
    print(f"   📄 Discovery: {discovery_file}")
    print(f"   📋 Registry: {registry_file}")
    print(f"   🧠 Auto-mapped: {registry['discovery']['auto_mapped']}/9 models")
    
    if registry['discovery']['auto_mapped'] == 9:
        print(f"   🎯 Status: ✅ ALL MODELS MAPPED SUCCESSFULLY")
    else:
        print(f"   🎯 Status: ⚠️ PARTIAL MAPPING - MANUAL REVIEW NEEDED")

if __name__ == "__main__":
    main()
