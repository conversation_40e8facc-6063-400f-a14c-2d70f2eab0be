#!/usr/bin/env python3
"""
💾 BOXSCORE STORAGE MANAGER
Advanced storage management for WNBA boxscore data
"""

import os
import shutil
import gzip
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import hashlib

class StorageManager:
    """Advanced storage management for boxscore data"""
    
    def __init__(self, data_path='data/boxscores', backup_path='backups/boxscores'):
        self.data_path = Path(data_path)
        self.backup_path = Path(backup_path)
        self.setup_directories()
        
    def setup_directories(self):
        """Create necessary directories"""
        self.data_path.mkdir(parents=True, exist_ok=True)
        self.backup_path.mkdir(parents=True, exist_ok=True)
        
    def get_file_info(self, file_path):
        """Get detailed file information"""
        stat = file_path.stat()
        return {
            'path': str(file_path),
            'size_bytes': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'created': datetime.fromtimestamp(stat.st_ctime),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'age_days': (datetime.now() - datetime.fromtimestamp(stat.st_mtime)).days
        }
    
    def calculate_checksum(self, file_path):
        """Calculate MD5 checksum for integrity verification"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def compress_file(self, file_path, remove_original=True):
        """Compress a file using gzip"""
        compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
        
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        if remove_original:
            file_path.unlink()
            
        return compressed_path
    
    def decompress_file(self, compressed_path, remove_compressed=False):
        """Decompress a gzipped file"""
        original_path = compressed_path.with_suffix('')
        
        with gzip.open(compressed_path, 'rb') as f_in:
            with open(original_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        if remove_compressed:
            compressed_path.unlink()
            
        return original_path
    
    def cleanup_old_files(self, retain_days=30, dry_run=False):
        """Remove files older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=retain_days)
        removed_files = []
        total_size_freed = 0
        
        print(f"🧹 CLEANUP: Removing files older than {retain_days} days")
        print(f"   📅 Cutoff date: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        for file_path in self.data_path.glob('*'):
            if file_path.is_file():
                file_info = self.get_file_info(file_path)
                
                if file_info['modified'] < cutoff_date:
                    print(f"   🗑️ {'[DRY RUN] ' if dry_run else ''}Removing: {file_path.name} ({file_info['size_mb']:.2f}MB)")
                    
                    if not dry_run:
                        file_path.unlink()
                    
                    removed_files.append(file_info)
                    total_size_freed += file_info['size_bytes']
        
        print(f"✅ Cleanup complete:")
        print(f"   📁 Files removed: {len(removed_files)}")
        print(f"   💾 Space freed: {total_size_freed / (1024*1024):.2f}MB")
        
        return removed_files
    
    def compress_old_files(self, compress_after_days=7, dry_run=False):
        """Compress files older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=compress_after_days)
        compressed_files = []
        total_size_saved = 0
        
        print(f"🗜️ COMPRESSION: Compressing files older than {compress_after_days} days")
        
        for file_path in self.data_path.glob('*.parquet'):
            if file_path.is_file():
                file_info = self.get_file_info(file_path)
                
                if file_info['modified'] < cutoff_date:
                    original_size = file_info['size_bytes']
                    
                    if not dry_run:
                        compressed_path = self.compress_file(file_path)
                        compressed_size = compressed_path.stat().st_size
                        compression_ratio = (1 - compressed_size / original_size) * 100
                        
                        print(f"   🗜️ Compressed: {file_path.name} ({compression_ratio:.1f}% reduction)")
                        
                        compressed_files.append({
                            'original': str(file_path),
                            'compressed': str(compressed_path),
                            'original_size': original_size,
                            'compressed_size': compressed_size,
                            'compression_ratio': compression_ratio
                        })
                        
                        total_size_saved += (original_size - compressed_size)
                    else:
                        print(f"   🗜️ [DRY RUN] Would compress: {file_path.name}")
        
        print(f"✅ Compression complete:")
        print(f"   📁 Files compressed: {len(compressed_files)}")
        print(f"   💾 Space saved: {total_size_saved / (1024*1024):.2f}MB")
        
        return compressed_files
    
    def backup_data(self, backup_name=None):
        """Create a backup of all boxscore data"""
        if backup_name is None:
            backup_name = f"boxscore_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_dir = self.backup_path / backup_name
        backup_dir.mkdir(exist_ok=True)
        
        print(f"💾 BACKUP: Creating backup '{backup_name}'")
        
        backed_up_files = []
        total_size = 0
        
        for file_path in self.data_path.glob('*'):
            if file_path.is_file():
                dest_path = backup_dir / file_path.name
                shutil.copy2(file_path, dest_path)
                
                file_info = self.get_file_info(file_path)
                backed_up_files.append(file_info)
                total_size += file_info['size_bytes']
                
                print(f"   📄 Backed up: {file_path.name}")
        
        # Create backup manifest
        manifest = {
            'backup_name': backup_name,
            'created_at': datetime.now().isoformat(),
            'files_count': len(backed_up_files),
            'total_size_mb': total_size / (1024*1024),
            'files': backed_up_files
        }
        
        manifest_path = backup_dir / 'manifest.json'
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2, default=str)
        
        print(f"✅ Backup complete:")
        print(f"   📁 Files backed up: {len(backed_up_files)}")
        print(f"   💾 Total size: {total_size / (1024*1024):.2f}MB")
        print(f"   📍 Location: {backup_dir}")
        
        return backup_dir
    
    def verify_integrity(self):
        """Verify integrity of all files"""
        print("🔍 INTEGRITY CHECK: Verifying all files")
        
        verified_files = []
        corrupted_files = []
        
        for file_path in self.data_path.glob('*.parquet'):
            try:
                # Try to read the parquet file
                df = pd.read_parquet(file_path)
                
                file_info = self.get_file_info(file_path)
                file_info['checksum'] = self.calculate_checksum(file_path)
                file_info['records'] = len(df)
                file_info['columns'] = list(df.columns)
                
                verified_files.append(file_info)
                print(f"   ✅ Valid: {file_path.name} ({len(df)} records)")
                
            except Exception as e:
                corrupted_files.append({
                    'path': str(file_path),
                    'error': str(e)
                })
                print(f"   ❌ Corrupted: {file_path.name} - {e}")
        
        print(f"✅ Integrity check complete:")
        print(f"   ✅ Valid files: {len(verified_files)}")
        print(f"   ❌ Corrupted files: {len(corrupted_files)}")
        
        return verified_files, corrupted_files
    
    def get_storage_stats(self):
        """Get comprehensive storage statistics"""
        stats = {
            'total_files': 0,
            'total_size_mb': 0,
            'file_types': {},
            'age_distribution': {'0-7_days': 0, '7-30_days': 0, '30+_days': 0},
            'largest_files': [],
            'oldest_files': [],
            'newest_files': []
        }
        
        all_files = []
        
        for file_path in self.data_path.glob('*'):
            if file_path.is_file():
                file_info = self.get_file_info(file_path)
                all_files.append(file_info)
                
                stats['total_files'] += 1
                stats['total_size_mb'] += file_info['size_mb']
                
                # File type distribution
                suffix = file_path.suffix
                stats['file_types'][suffix] = stats['file_types'].get(suffix, 0) + 1
                
                # Age distribution
                age_days = file_info['age_days']
                if age_days <= 7:
                    stats['age_distribution']['0-7_days'] += 1
                elif age_days <= 30:
                    stats['age_distribution']['7-30_days'] += 1
                else:
                    stats['age_distribution']['30+_days'] += 1
        
        # Sort files by size and age
        all_files.sort(key=lambda x: x['size_bytes'], reverse=True)
        stats['largest_files'] = all_files[:5]
        
        all_files.sort(key=lambda x: x['modified'])
        stats['oldest_files'] = all_files[:5]
        stats['newest_files'] = all_files[-5:]
        
        return stats
    
    def print_storage_report(self):
        """Print comprehensive storage report"""
        stats = self.get_storage_stats()
        
        print("📊 STORAGE REPORT")
        print("=" * 50)
        print(f"📁 Total Files: {stats['total_files']}")
        print(f"💾 Total Size: {stats['total_size_mb']:.2f}MB")
        print(f"📍 Data Path: {self.data_path}")
        
        print(f"\n📈 File Types:")
        for file_type, count in stats['file_types'].items():
            print(f"   {file_type or 'no extension'}: {count} files")
        
        print(f"\n📅 Age Distribution:")
        for age_range, count in stats['age_distribution'].items():
            print(f"   {age_range}: {count} files")
        
        print(f"\n🏆 Largest Files:")
        for file_info in stats['largest_files']:
            print(f"   {Path(file_info['path']).name}: {file_info['size_mb']:.2f}MB")
        
        print(f"\n⏰ Oldest Files:")
        for file_info in stats['oldest_files']:
            print(f"   {Path(file_info['path']).name}: {file_info['modified'].strftime('%Y-%m-%d')}")

def main():
    parser = argparse.ArgumentParser(description="💾 Boxscore Storage Manager")
    parser.add_argument("--path", default="data/boxscores", help="Data directory path")
    parser.add_argument("--backup-dir", default="backups/boxscores", help="Backup directory path")
    parser.add_argument("--retain", type=int, default=30, help="Days to retain files")
    parser.add_argument("--compress", action="store_true", help="Compress old files")
    parser.add_argument("--compress-after", type=int, default=7, help="Days before compression")
    parser.add_argument("--cleanup", action="store_true", help="Remove old files")
    parser.add_argument("--backup", action="store_true", help="Create backup")
    parser.add_argument("--verify", action="store_true", help="Verify file integrity")
    parser.add_argument("--stats", action="store_true", help="Show storage statistics")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done")
    
    args = parser.parse_args()
    
    manager = StorageManager(args.path, args.backup_dir)
    
    if args.stats:
        manager.print_storage_report()
    
    if args.verify:
        manager.verify_integrity()
    
    if args.backup:
        manager.backup_data()
    
    if args.compress:
        manager.compress_old_files(args.compress_after, args.dry_run)
    
    if args.cleanup:
        manager.cleanup_old_files(args.retain, args.dry_run)
    
    if not any([args.stats, args.verify, args.backup, args.compress, args.cleanup]):
        print("💾 Boxscore Storage Manager")
        print("Use --stats, --verify, --backup, --compress, or --cleanup")

if __name__ == "__main__":
    main()
