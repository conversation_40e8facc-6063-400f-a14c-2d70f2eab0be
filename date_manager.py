#!/usr/bin/env python3
"""
📅 DATE MANAGER FOR HYPER_MEDUSA_NEURAL_VAULT
Centralized date handling to ensure correct dates throughout the system
"""

import json
from datetime import datetime, timedelta
from pathlib import Path

class DateManager:
    """Centralized date management for the validation system"""
    
    def __init__(self, config_file='config/date_config.json'):
        self.config_file = Path(config_file)
        self.config = self._load_config()
    
    def _load_config(self):
        """Load date configuration"""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                return json.load(f)
        else:
            # Create default config
            default_config = {
                "current_date": "2025-07-07",
                "timezone": "UTC",
                "auto_update": True,
                "validation_offset_days": 1,
                "season_start": "2025-05-01",
                "season_end": "2025-10-31"
            }
            self._save_config(default_config)
            return default_config
    
    def _save_config(self, config):
        """Save date configuration"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
    
    def get_current_date(self):
        """Get the current date for the system"""
        if self.config.get('auto_update', True):
            # Use actual current date
            return datetime.now().date()
        else:
            # Use configured date
            date_str = self.config.get('current_date', '2025-07-07')
            return datetime.strptime(date_str, '%Y-%m-%d').date()
    
    def get_validation_date(self):
        """Get the date for validation (usually yesterday)"""
        current = self.get_current_date()
        offset = self.config.get('validation_offset_days', 1)
        return current - timedelta(days=offset)
    
    def get_prediction_date(self):
        """Get the date for making predictions (usually today)"""
        return self.get_current_date()
    
    def set_current_date(self, date_str):
        """Manually set the current date (for testing)"""
        try:
            # Validate date format
            datetime.strptime(date_str, '%Y-%m-%d')
            self.config['current_date'] = date_str
            self.config['auto_update'] = False
            self._save_config(self.config)
            print(f"✅ Date set to: {date_str}")
        except ValueError:
            print(f"❌ Invalid date format: {date_str}. Use YYYY-MM-DD")
    
    def enable_auto_update(self):
        """Enable automatic date updates"""
        self.config['auto_update'] = True
        self._save_config(self.config)
        print("✅ Auto-update enabled - using system date")
    
    def disable_auto_update(self):
        """Disable automatic date updates"""
        self.config['auto_update'] = False
        self._save_config(self.config)
        print("✅ Auto-update disabled - using configured date")
    
    def get_date_range(self, days_back=7):
        """Get a range of dates for validation"""
        end_date = self.get_validation_date()
        start_date = end_date - timedelta(days=days_back-1)
        
        dates = []
        current = start_date
        while current <= end_date:
            dates.append(current)
            current += timedelta(days=1)
        
        return dates
    
    def is_game_day(self, date=None):
        """Check if a date is a potential game day"""
        if date is None:
            date = self.get_current_date()
        
        # WNBA typically plays Tuesday through Sunday
        weekday = date.weekday()  # 0=Monday, 6=Sunday
        return weekday in [1, 2, 3, 4, 5, 6]  # Tue-Sun
    
    def get_status(self):
        """Get current date manager status"""
        current = self.get_current_date()
        validation = self.get_validation_date()
        prediction = self.get_prediction_date()
        
        return {
            'current_date': current.strftime('%Y-%m-%d'),
            'validation_date': validation.strftime('%Y-%m-%d'),
            'prediction_date': prediction.strftime('%Y-%m-%d'),
            'auto_update': self.config.get('auto_update', True),
            'timezone': self.config.get('timezone', 'UTC'),
            'is_game_day': self.is_game_day(current)
        }

def main():
    """Command line interface for date management"""
    import argparse
    
    parser = argparse.ArgumentParser(description="📅 Date Manager for HYPER_MEDUSA_NEURAL_VAULT")
    parser.add_argument("--status", action="store_true", help="Show current date status")
    parser.add_argument("--set-date", help="Set current date (YYYY-MM-DD)")
    parser.add_argument("--auto-update", action="store_true", help="Enable auto-update")
    parser.add_argument("--no-auto-update", action="store_true", help="Disable auto-update")
    parser.add_argument("--validation-date", action="store_true", help="Show validation date")
    parser.add_argument("--prediction-date", action="store_true", help="Show prediction date")
    parser.add_argument("--range", type=int, help="Show date range (days back)")
    
    args = parser.parse_args()
    
    dm = DateManager()
    
    if args.status:
        print("📅 DATE MANAGER STATUS")
        print("=" * 30)
        status = dm.get_status()
        for key, value in status.items():
            emoji = "✅" if value else "❌" if isinstance(value, bool) else "📅"
            print(f"   {emoji} {key.replace('_', ' ').title()}: {value}")
    
    elif args.set_date:
        dm.set_current_date(args.set_date)
        print("\n📅 Updated Status:")
        status = dm.get_status()
        print(f"   📅 Current Date: {status['current_date']}")
        print(f"   📅 Auto Update: {status['auto_update']}")
    
    elif args.auto_update:
        dm.enable_auto_update()
        print("\n📅 Updated Status:")
        status = dm.get_status()
        print(f"   📅 Current Date: {status['current_date']}")
        print(f"   ✅ Auto Update: {status['auto_update']}")
    
    elif args.no_auto_update:
        dm.disable_auto_update()
        print("\n📅 Updated Status:")
        status = dm.get_status()
        print(f"   📅 Current Date: {status['current_date']}")
        print(f"   ❌ Auto Update: {status['auto_update']}")
    
    elif args.validation_date:
        validation_date = dm.get_validation_date()
        print(f"📅 Validation Date: {validation_date.strftime('%Y-%m-%d')}")
    
    elif args.prediction_date:
        prediction_date = dm.get_prediction_date()
        print(f"📅 Prediction Date: {prediction_date.strftime('%Y-%m-%d')}")
    
    elif args.range:
        dates = dm.get_date_range(args.range)
        print(f"📅 Date Range ({args.range} days):")
        for i, date in enumerate(dates):
            marker = "📍" if i == len(dates) - 1 else "📅"
            game_day = "🏀" if dm.is_game_day(date) else "⭕"
            print(f"   {marker} {date.strftime('%Y-%m-%d')} {game_day}")
    
    else:
        # Default: show status
        print("📅 HYPER_MEDUSA_NEURAL_VAULT DATE MANAGER")
        print("=" * 50)
        status = dm.get_status()
        print(f"📅 Current Date: {status['current_date']}")
        print(f"📊 Validation Date: {status['validation_date']}")
        print(f"🔮 Prediction Date: {status['prediction_date']}")
        print(f"🔄 Auto Update: {'✅' if status['auto_update'] else '❌'}")
        print(f"🏀 Game Day: {'✅' if status['is_game_day'] else '❌'}")
        print()
        print("💡 Commands:")
        print("   --status              Show detailed status")
        print("   --set-date 2025-07-07 Set specific date")
        print("   --auto-update         Enable auto-update")
        print("   --no-auto-update      Disable auto-update")
        print("   --validation-date     Show validation date")
        print("   --range 7             Show 7-day range")

if __name__ == "__main__":
    main()
