#!/usr/bin/env python3
"""
📋 BOXSCORE LISTING UTILITY
List and analyze stored boxscore data
"""

import argparse
import pandas as pd
import json
from datetime import datetime, timedelta
from pathlib import Path
from tabulate import tabulate

class BoxscoreLister:
    """Utility for listing and analyzing boxscore data"""
    
    def __init__(self, data_path='data/boxscores'):
        self.data_path = Path(data_path)
        
    def get_available_games(self):
        """Get list of all available games"""
        games = []
        
        for player_file in self.data_path.glob('*_players.parquet'):
            game_id = player_file.stem.replace('_players', '')
            
            # Look for corresponding files
            team_file = self.data_path / f'{game_id}_teams.parquet'
            metadata_file = self.data_path / f'{game_id}_metadata.json'
            
            game_info = {
                'game_id': game_id,
                'player_file': player_file,
                'team_file': team_file if team_file.exists() else None,
                'metadata_file': metadata_file if metadata_file.exists() else None,
                'file_size_mb': player_file.stat().st_size / (1024*1024),
                'modified': datetime.fromtimestamp(player_file.stat().st_mtime)
            }
            
            # Load metadata if available
            if metadata_file.exists():
                try:
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    game_info.update(metadata)
                except:
                    pass
            
            games.append(game_info)
        
        return sorted(games, key=lambda x: x['modified'], reverse=True)
    
    def list_recent_games(self, days=1, format_type='table'):
        """List games from recent days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        all_games = self.get_available_games()
        
        recent_games = [g for g in all_games if g['modified'] > cutoff_date]
        
        print(f"📋 RECENT BOXSCORES ({days} day{'s' if days != 1 else ''})")
        print("=" * 60)
        print(f"📅 Since: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Found: {len(recent_games)} games")
        
        if not recent_games:
            print("📭 No recent games found")
            return
        
        if format_type == 'table':
            self._print_games_table(recent_games)
        elif format_type == 'json':
            self._print_games_json(recent_games)
        elif format_type == 'summary':
            self._print_games_summary(recent_games)
    
    def list_all_games(self, format_type='table'):
        """List all available games"""
        all_games = self.get_available_games()
        
        print(f"📋 ALL STORED BOXSCORES")
        print("=" * 60)
        print(f"🎯 Total: {len(all_games)} games")
        
        if not all_games:
            print("📭 No games found")
            return
        
        if format_type == 'table':
            self._print_games_table(all_games)
        elif format_type == 'json':
            self._print_games_json(all_games)
        elif format_type == 'summary':
            self._print_games_summary(all_games)
    
    def _print_games_table(self, games):
        """Print games in table format"""
        table_data = []
        
        for game in games:
            row = [
                game['game_id'],
                game['modified'].strftime('%Y-%m-%d %H:%M'),
                f"{game['file_size_mb']:.2f}MB",
                game.get('player_records', 'N/A'),
                game.get('team_records', 'N/A'),
                '✅' if game['team_file'] else '❌',
                '✅' if game['metadata_file'] else '❌'
            ]
            table_data.append(row)
        
        headers = ['Game ID', 'Modified', 'Size', 'Players', 'Teams', 'Team File', 'Metadata']
        
        print(tabulate(table_data, headers=headers, tablefmt='grid'))
    
    def _print_games_json(self, games):
        """Print games in JSON format"""
        games_data = []
        for game in games:
            game_data = {
                'game_id': game['game_id'],
                'modified': game['modified'].isoformat(),
                'file_size_mb': round(game['file_size_mb'], 2),
                'player_records': game.get('player_records'),
                'team_records': game.get('team_records'),
                'has_team_file': game['team_file'] is not None,
                'has_metadata': game['metadata_file'] is not None
            }
            games_data.append(game_data)
        
        print(json.dumps(games_data, indent=2))
    
    def _print_games_summary(self, games):
        """Print games summary"""
        total_size = sum(g['file_size_mb'] for g in games)
        total_players = sum(g.get('player_records', 0) for g in games if g.get('player_records'))
        
        with_team_files = sum(1 for g in games if g['team_file'])
        with_metadata = sum(1 for g in games if g['metadata_file'])
        
        print(f"📊 SUMMARY:")
        print(f"   🎮 Total Games: {len(games)}")
        print(f"   💾 Total Size: {total_size:.2f}MB")
        print(f"   👥 Total Players: {total_players}")
        print(f"   🏀 With Team Files: {with_team_files}/{len(games)}")
        print(f"   📋 With Metadata: {with_metadata}/{len(games)}")
        
        if games:
            print(f"   📅 Date Range: {games[-1]['modified'].strftime('%Y-%m-%d')} to {games[0]['modified'].strftime('%Y-%m-%d')}")
    
    def analyze_game(self, game_id):
        """Analyze a specific game in detail"""
        print(f"🔍 GAME ANALYSIS: {game_id}")
        print("=" * 50)
        
        player_file = self.data_path / f'{game_id}_players.parquet'
        team_file = self.data_path / f'{game_id}_teams.parquet'
        metadata_file = self.data_path / f'{game_id}_metadata.json'
        
        if not player_file.exists():
            print(f"❌ Game {game_id} not found")
            return
        
        # Load player data
        try:
            players_df = pd.read_parquet(player_file)
            print(f"👥 PLAYER STATS:")
            print(f"   📊 Records: {len(players_df)}")
            print(f"   📋 Columns: {len(players_df.columns)}")
            
            if 'PTS' in players_df.columns:
                print(f"   🏆 Top Scorer: {players_df.loc[players_df['PTS'].idxmax(), 'PLAYER_NAME']} ({players_df['PTS'].max()} pts)")
            
            if 'REB' in players_df.columns:
                print(f"   🏀 Top Rebounder: {players_df.loc[players_df['REB'].idxmax(), 'PLAYER_NAME']} ({players_df['REB'].max()} reb)")
            
            if 'AST' in players_df.columns:
                print(f"   🎯 Top Assists: {players_df.loc[players_df['AST'].idxmax(), 'PLAYER_NAME']} ({players_df['AST'].max()} ast)")
            
            # Team breakdown
            if 'TEAM_ABBREVIATION' in players_df.columns:
                team_stats = players_df.groupby('TEAM_ABBREVIATION').agg({
                    'PTS': 'sum' if 'PTS' in players_df.columns else 'count',
                    'REB': 'sum' if 'REB' in players_df.columns else 'count',
                    'AST': 'sum' if 'AST' in players_df.columns else 'count'
                }).round(1)
                
                print(f"\n🏀 TEAM TOTALS:")
                print(team_stats)
            
        except Exception as e:
            print(f"❌ Error loading player data: {e}")
        
        # Load team data
        if team_file.exists():
            try:
                teams_df = pd.read_parquet(team_file)
                print(f"\n🏀 TEAM STATS:")
                print(f"   📊 Records: {len(teams_df)}")
                print(teams_df[['TEAM_NAME', 'PTS']].to_string(index=False) if 'TEAM_NAME' in teams_df.columns else teams_df.head())
                
            except Exception as e:
                print(f"❌ Error loading team data: {e}")
        
        # Load metadata
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                print(f"\n📋 METADATA:")
                print(f"   📅 Retrieved: {metadata.get('retrieved_at', 'Unknown')}")
                print(f"   💾 File Sizes: {metadata.get('file_size_kb', {})}")
                
            except Exception as e:
                print(f"❌ Error loading metadata: {e}")
    
    def search_games(self, query, field='all'):
        """Search games by various criteria"""
        all_games = self.get_available_games()
        matching_games = []
        
        print(f"🔍 SEARCHING GAMES")
        print("=" * 40)
        print(f"🎯 Query: '{query}'")
        print(f"📋 Field: {field}")
        
        for game in all_games:
            match = False
            
            if field == 'all' or field == 'game_id':
                if query.lower() in game['game_id'].lower():
                    match = True
            
            if field == 'all' or field == 'date':
                if query in game['modified'].strftime('%Y-%m-%d'):
                    match = True
            
            # Search in player data if requested
            if field == 'all' or field == 'player':
                try:
                    players_df = pd.read_parquet(game['player_file'])
                    if 'PLAYER_NAME' in players_df.columns:
                        if players_df['PLAYER_NAME'].str.contains(query, case=False, na=False).any():
                            match = True
                except:
                    pass
            
            if match:
                matching_games.append(game)
        
        print(f"✅ Found {len(matching_games)} matching games")
        
        if matching_games:
            self._print_games_table(matching_games)
        else:
            print("📭 No matching games found")

def main():
    parser = argparse.ArgumentParser(description="📋 Boxscore Listing Utility")
    parser.add_argument("--days", type=int, default=1, help="Days to look back")
    parser.add_argument("--format", choices=['table', 'json', 'summary'], default='table', help="Output format")
    parser.add_argument("--all", action="store_true", help="List all games")
    parser.add_argument("--analyze", help="Analyze specific game ID")
    parser.add_argument("--search", help="Search games")
    parser.add_argument("--search-field", choices=['all', 'game_id', 'date', 'player'], default='all', help="Search field")
    parser.add_argument("--path", default="data/boxscores", help="Data directory path")
    
    args = parser.parse_args()
    
    lister = BoxscoreLister(args.path)
    
    if args.analyze:
        lister.analyze_game(args.analyze)
    elif args.search:
        lister.search_games(args.search, args.search_field)
    elif args.all:
        lister.list_all_games(args.format)
    else:
        lister.list_recent_games(args.days, args.format)

if __name__ == "__main__":
    main()
