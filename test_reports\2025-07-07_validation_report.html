
        <!DOCTYPE html>
        <html>
        <head>
            <title>HYPER_MEDUSA_NEURAL_VAULT Validation Report - 2025-07-07</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; }
                .metric-card { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
                .success { border-left-color: #28a745; }
                .warning { border-left-color: #ffc107; }
                .error { border-left-color: #dc3545; }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏀 HYPER_MEDUSA_NEURAL_VAULT</h1>
                <h2>Prediction Validation Report - 2025-07-07</h2>
                <p>Generated: 2025-07-07 19:43:49</p>
            </div>
            
            <div class="grid">
                <div class="metric-card">
                    <h3>📊 Overall Performance</h3>
                    <p><strong>Total Predictions:</strong> 22</p>
                    <p><strong>Player Props:</strong> 16</p>
                    <p><strong>Game Outcomes:</strong> 6</p>
                    <p><strong>Avg Player Error:</strong> 0.5</p>
                    <p><strong>Avg Game Error:</strong> 7.95</p>
                </div>
                
                <div class="metric-card">
                    <h3>🎯 Player Props Accuracy</h3>
                    <table><tr><th>Stat</th><th>MAE</th><th>Accuracy</th><th>Within Threshold</th></tr>
            <tr>
                <td>Points</td>
                <td>1.05</td>
                <td>50.0%</td>
                <td>100.0%</td>
            </tr>
            
            <tr>
                <td>Rebounds</td>
                <td>0.35</td>
                <td>100.0%</td>
                <td>100.0%</td>
            </tr>
            
            <tr>
                <td>Assists</td>
                <td>0.18</td>
                <td>100.0%</td>
                <td>100.0%</td>
            </tr>
            
            <tr>
                <td>Blocks</td>
                <td>0.2</td>
                <td>100.0%</td>
                <td>100.0%</td>
            </tr>
            
            <tr>
                <td>Threes</td>
                <td>0.35</td>
                <td>100.0%</td>
                <td>100.0%</td>
            </tr>
            
            <tr>
                <td>Steals</td>
                <td>0.8</td>
                <td>100.0%</td>
                <td>100.0%</td>
            </tr>
            </table>
                </div>
                
                <div class="metric-card">
                    <h3>🏀 Game Outcomes Accuracy</h3>
                    <table><tr><th>Outcome</th><th>Accuracy</th><th>MAE</th></tr>
            <tr>
                <td>Moneyline</td>
                <td>0.0%</td>
                <td>N/A</td>
            </tr>
            
            <tr>
                <td>Spread</td>
                <td>0.0%</td>
                <td>19.0</td>
            </tr>
            
            <tr>
                <td>Total</td>
                <td>100.0%</td>
                <td>4.5</td>
            </tr>
            </table>
                </div>
            </div>
            
            <div class="metric-card">
                <h3>💡 Key Insights</h3>
                
            <div class="metric-card warning">
                <strong>Points model accuracy (50.0%) below 60% threshold</strong><br>
                <em>Recommendation: Consider retraining points model with recent data</em>
            </div>
            
            <div class="metric-card success">
                <strong>Rebounds model performing excellently (100.0%)</strong><br>
                <em>Recommendation: Maintain current model parameters</em>
            </div>
            
            <div class="metric-card success">
                <strong>Assists model performing excellently (100.0%)</strong><br>
                <em>Recommendation: Maintain current model parameters</em>
            </div>
            
            <div class="metric-card success">
                <strong>Blocks model performing excellently (100.0%)</strong><br>
                <em>Recommendation: Maintain current model parameters</em>
            </div>
            
            <div class="metric-card success">
                <strong>Threes model performing excellently (100.0%)</strong><br>
                <em>Recommendation: Maintain current model parameters</em>
            </div>
            
            <div class="metric-card success">
                <strong>Steals model performing excellently (100.0%)</strong><br>
                <em>Recommendation: Maintain current model parameters</em>
            </div>
            
            <div class="metric-card warning">
                <strong>Moneyline predictions below 55% accuracy</strong><br>
                <em>Recommendation: Review moneyline model features and training data</em>
            </div>
            
            <div class="metric-card warning">
                <strong>Spread predictions below 55% accuracy</strong><br>
                <em>Recommendation: Review spread model features and training data</em>
            </div>
            
            </div>
            
            <div class="metric-card">
                <h3>📈 Best Predictions</h3>
                
            <p><strong>Rhyne Howard rebounds:</strong> 
            Predicted 4.1, Actual 4 
            (Error: 0.09999999999999964)</p>
            
            </div>
            
            <div class="metric-card">
                <h3>📉 Worst Predictions</h3>
                
            <p><strong>A'ja Wilson points:</strong> 
            Predicted 24.5, Actual 26 
            (Error: 1.5)</p>
            
            </div>
        </body>
        </html>
        