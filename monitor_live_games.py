#!/usr/bin/env python3
"""
🔴 LIVE WNBA GAMES MONITOR
Real-time monitoring of today's WNBA games
"""

import time
from datetime import datetime
from nba_api.stats.endpoints import scoreboardv2

def monitor_games():
    """Monitor live WNBA games"""
    print("🔴 LIVE WNBA GAMES MONITOR")
    print("=" * 50)
    print("🎯 Monitoring games: 1022500119, 1022500120")
    print("⏰ Checking every 2 minutes...")
    print("🛑 Press Ctrl+C to stop")
    print()
    
    target_games = ['1022500119', '1022500120']
    
    try:
        while True:
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"🕐 {current_time} - Checking game status...")
            
            try:
                # Get current scoreboard
                board = scoreboardv2.ScoreboardV2(league_id='10', day_offset=0)
                games_df = board.get_data_frames()[0]
                
                for _, game in games_df.iterrows():
                    game_id = str(game.get('GAME_ID', ''))
                    
                    if game_id in target_games:
                        status = game.get('GAME_STATUS_TEXT', 'Unknown')
                        home_team = game.get('HOME_TEAM_ABBREVIATION', 'HOME')
                        away_team = game.get('VISITOR_TEAM_ABBREVIATION', 'AWAY')
                        
                        # Try to get scores if available
                        home_score = game.get('PTS_HOME', '')
                        away_score = game.get('PTS_AWAY', '')
                        
                        score_display = ""
                        if home_score and away_score:
                            score_display = f" ({away_team} {away_score} - {home_team} {home_score})"
                        
                        print(f"   🏀 Game {game_id}: {away_team} @ {home_team}{score_display}")
                        print(f"      📊 Status: {status}")
                        
                        # Check if game is final
                        if status == 'Final':
                            print(f"      ✅ GAME COMPLETED! Boxscore will be retrieved soon.")
                        elif 'Q' in status or 'Half' in status or 'OT' in status:
                            print(f"      🔴 LIVE: {status}")
                        else:
                            print(f"      ⏳ Waiting to start...")
                
                print()
                
            except Exception as e:
                print(f"   ❌ Error checking games: {e}")
            
            # Wait 2 minutes before next check
            time.sleep(120)
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")

if __name__ == "__main__":
    monitor_games()
