#!/usr/bin/env python3
"""
🧪 Local API Test Script
Test the HYPER_MEDUSA_NEURAL_VAULT local server
"""

import requests
import json
import time
from datetime import datetime

def test_local_api():
    """Test the local prediction API"""
    
    base_url = "http://localhost:8080"
    
    print("🧪 TESTING LOCAL HYPER_MEDUSA_NEURAL_VAULT API")
    print("=" * 50)
    
    # Test 1: Health Check
    print("\n1. 🏥 Health Check")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✅ Status: {health_data['status']}")
            print(f"   🖥️ Device: {health_data['device']}")
            print(f"   ⚡ GPU Available: {health_data['gpu_available']}")
            print(f"   ⏱️ Uptime: {health_data['uptime_seconds']:.1f}s")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Health check error: {str(e)}")
        return
    
    # Test 2: List Models
    print("\n2. 🧠 Available Models")
    try:
        response = requests.get(f"{base_url}/models")
        if response.status_code == 200:
            models_data = response.json()
            print(f"   📋 Available: {', '.join(models_data['available_models'])}")
            print(f"   🔄 Loaded: {', '.join(models_data['loaded_models']) if models_data['loaded_models'] else 'None yet'}")
            print(f"   ⭐ Priority: {', '.join(models_data['priority_models'])}")
        else:
            print(f"   ❌ Models list failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Models list error: {str(e)}")
    
    # Test 3: Player Points Prediction
    print("\n3. 🏀 Player Points Prediction")
    try:
        prediction_request = {
            "model": "player_points",
            "player_id": 203
        }
        
        start_time = time.time()
        response = requests.post(f"{base_url}/predict", json=prediction_request)
        end_time = time.time()
        
        if response.status_code == 200:
            prediction_data = response.json()
            print(f"   ✅ Prediction: {prediction_data['prediction']} points")
            print(f"   🎯 Confidence: {prediction_data['confidence']:.1%}")
            print(f"   ⚡ Latency: {(end_time - start_time) * 1000:.2f}ms")
            print(f"   🧠 Model Loaded: {prediction_data.get('model_loaded', 'Unknown')}")
            print(f"   📁 Model File: {prediction_data.get('model_file', 'Unknown')}")
            if prediction_data.get('fallback'):
                print(f"   ⚠️ Fallback Mode: {prediction_data.get('warning', '')}")
        else:
            print(f"   ❌ Prediction failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Prediction error: {str(e)}")
    
    # Test 4: Game Totals Prediction
    print("\n4. 🎯 Game Totals Prediction")
    try:
        prediction_request = {
            "model": "game_totals",
            "team_id": 1
        }
        
        start_time = time.time()
        response = requests.post(f"{base_url}/predict", json=prediction_request)
        end_time = time.time()
        
        if response.status_code == 200:
            prediction_data = response.json()
            print(f"   ✅ Prediction: {prediction_data['prediction']} total points")
            print(f"   🎯 Confidence: {prediction_data['confidence']:.1%}")
            print(f"   ⚡ Latency: {(end_time - start_time) * 1000:.2f}ms")
            print(f"   🧠 Model Loaded: {prediction_data.get('model_loaded', 'Unknown')}")
        else:
            print(f"   ❌ Prediction failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Prediction error: {str(e)}")
    
    # Test 5: Moneyline Prediction
    print("\n5. 💰 Moneyline Prediction")
    try:
        prediction_request = {
            "model": "moneyline",
            "team_id": 2
        }
        
        start_time = time.time()
        response = requests.post(f"{base_url}/predict", json=prediction_request)
        end_time = time.time()
        
        if response.status_code == 200:
            prediction_data = response.json()
            print(f"   ✅ Win Probability: {prediction_data['prediction']:.1%}")
            print(f"   🎯 Confidence: {prediction_data['confidence']:.1%}")
            print(f"   ⚡ Latency: {(end_time - start_time) * 1000:.2f}ms")
            print(f"   🧠 Model Loaded: {prediction_data.get('model_loaded', 'Unknown')}")
        else:
            print(f"   ❌ Prediction failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Prediction error: {str(e)}")
    
    # Test 6: Batch Prediction
    print("\n6. 📦 Batch Prediction Test")
    try:
        batch_requests = [
            {"model": "player_points", "player_id": 203},
            {"model": "rebounds", "player_id": 204},
            {"model": "assists", "player_id": 205},
            {"model": "threes", "player_id": 206}
        ]
        
        start_time = time.time()
        response = requests.post(f"{base_url}/predict/batch", json=batch_requests)
        end_time = time.time()
        
        if response.status_code == 200:
            batch_data = response.json()
            print(f"   ✅ Batch Size: {batch_data['batch_size']} requests")
            print(f"   ⚡ Total Latency: {batch_data['total_latency_ms']:.2f}ms")
            print(f"   📊 Avg per Request: {batch_data['avg_latency_per_request_ms']:.2f}ms")
            print(f"   🎯 Results: {len(batch_data['results'])} predictions")
            
            # Show first result
            if batch_data['results']:
                first_result = batch_data['results'][0]
                print(f"   📋 Sample: {first_result['model']} = {first_result['prediction']}")
        else:
            print(f"   ❌ Batch prediction failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Batch prediction error: {str(e)}")
    
    # Test 7: Performance Metrics
    print("\n7. 📊 Performance Metrics")
    try:
        response = requests.get(f"{base_url}/metrics")
        if response.status_code == 200:
            metrics_data = response.json()
            print(f"   📈 Requests Served: {metrics_data['requests_served']}")
            print(f"   ⚡ Avg Latency: {metrics_data['avg_latency_ms']:.2f}ms")
            print(f"   🎯 Cache Hit Rate: {(metrics_data['cache_hits'] / max(1, metrics_data['cache_hits'] + metrics_data['cache_misses']) * 100):.1f}%")
            print(f"   🧠 Models Loaded: {metrics_data['models_loaded']}/8")
            print(f"   💻 CPU Usage: {metrics_data['system']['cpu_percent']:.1f}%")
            print(f"   💾 Memory Usage: {metrics_data['system']['memory_percent']:.1f}%")
            print(f"   🖥️ GPU Memory: {metrics_data['system']['gpu_memory_gb']:.2f}GB")
        else:
            print(f"   ❌ Metrics failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Metrics error: {str(e)}")
    
    # Test 8: Load All Models
    print("\n8. 🔄 Loading All Models")
    models_to_test = ['player_points', 'game_totals', 'moneyline', 'rebounds', 'assists', 'threes', 'steals', 'blocks']
    
    loaded_models = []
    total_load_time = 0
    
    for model_name in models_to_test:
        try:
            print(f"   🔄 Loading {model_name}...")
            
            prediction_request = {
                "model": model_name,
                "player_id": 203 if model_name != 'moneyline' and model_name != 'game_totals' else None,
                "team_id": 1 if model_name == 'moneyline' or model_name == 'game_totals' else None
            }
            
            start_time = time.time()
            response = requests.post(f"{base_url}/predict", json=prediction_request)
            end_time = time.time()
            
            load_time = (end_time - start_time) * 1000
            total_load_time += load_time
            
            if response.status_code == 200:
                prediction_data = response.json()
                if prediction_data.get('model_loaded', False):
                    loaded_models.append(model_name)
                    print(f"      ✅ {model_name}: {prediction_data['prediction']} ({load_time:.1f}ms)")
                else:
                    print(f"      ⚠️ {model_name}: Fallback mode ({load_time:.1f}ms)")
            else:
                print(f"      ❌ {model_name}: Failed to load")
                
        except Exception as e:
            print(f"      ❌ {model_name}: Error - {str(e)}")
    
    print(f"\n   📊 Summary:")
    print(f"      🧠 Models Loaded: {len(loaded_models)}/8")
    print(f"      ⚡ Total Load Time: {total_load_time:.1f}ms")
    print(f"      📋 Loaded Models: {', '.join(loaded_models)}")
    
    print("\n🎊 LOCAL API TEST COMPLETE!")
    print(f"   🌐 Dashboard: http://localhost:8080/dashboard")
    print(f"   📚 API Docs: http://localhost:8080/docs")
    print(f"   ⏰ Test completed at: {datetime.now().strftime('%H:%M:%S')}")

if __name__ == "__main__":
    test_local_api()
