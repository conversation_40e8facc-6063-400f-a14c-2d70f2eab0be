#!/usr/bin/env python3
"""
🏀 DAILY VALIDATION RUNNER
Runs validation using the correct dates from DateManager
"""

from datetime import datetime
from date_manager import DateManager
from prediction_analyzer import PredictionValidator

def run_daily_validation():
    """Run daily validation with correct dates"""
    print("🏀 HYPER_MEDUSA_NEURAL_VAULT DAILY VALIDATION")
    print("=" * 60)
    
    # Initialize date manager
    dm = DateManager()
    
    # Show current date status
    status = dm.get_status()
    print("📅 DATE STATUS:")
    print(f"   📅 Current Date: {status['current_date']}")
    print(f"   📊 Validation Date: {status['validation_date']}")
    print(f"   🔮 Prediction Date: {status['prediction_date']}")
    print(f"   🔄 Auto Update: {'✅' if status['auto_update'] else '❌'}")
    print(f"   🏀 Game Day: {'✅' if status['is_game_day'] else '❌'}")
    print()
    
    # Get validation date
    validation_date = dm.get_validation_date()
    validation_datetime = datetime.combine(validation_date, datetime.min.time())
    
    print(f"🔍 Running validation for: {validation_date.strftime('%Y-%m-%d')}")
    print()
    
    # Create validator and run
    validator = PredictionValidator()
    results = validator.run_validation(validation_datetime)
    
    if results:
        print("\n🎯 VALIDATION SUMMARY:")
        print(f"   📊 Total Predictions: {results['overall']['total_predictions']}")
        print(f"   👥 Player Props: {results['overall']['player_prop_count']}")
        print(f"   🏀 Game Outcomes: {results['overall']['game_outcome_count']}")
        print(f"   📈 Avg Player Error: {results['overall']['avg_player_error']}")
        print(f"   📈 Avg Game Error: {results['overall']['avg_game_error']}")
        print(f"   💡 Insights Generated: {len(results['insights'])}")
        
        # Show key insights
        if results['insights']:
            print("\n💡 KEY INSIGHTS:")
            for insight in results['insights'][:5]:  # Show top 5
                emoji = "⚠️" if insight['type'] == 'warning' else "✅"
                print(f"   {emoji} {insight['message']}")
        
        print(f"\n📄 Report saved to: reports/validation/{validation_date.strftime('%Y-%m-%d')}_validation_report.html")
        return True
    else:
        print("❌ Validation failed - no results generated")
        return False

def main():
    """Main function with command line options"""
    import argparse
    
    parser = argparse.ArgumentParser(description="🏀 Daily Validation Runner")
    parser.add_argument("--date", help="Override validation date (YYYY-MM-DD)")
    parser.add_argument("--auto-update", action="store_true", help="Enable auto-update before running")
    parser.add_argument("--range", type=int, help="Run validation for multiple days back")
    
    args = parser.parse_args()
    
    dm = DateManager()
    
    # Enable auto-update if requested
    if args.auto_update:
        dm.enable_auto_update()
        print("✅ Auto-update enabled")
    
    # Override date if provided
    if args.date:
        dm.set_current_date(args.date)
        print(f"✅ Date override: {args.date}")
    
    # Run for date range if specified
    if args.range:
        print(f"🔄 Running validation for {args.range} days...")
        dates = dm.get_date_range(args.range)
        success_count = 0
        
        for date in dates:
            print(f"\n📅 Validating {date.strftime('%Y-%m-%d')}...")
            validation_datetime = datetime.combine(date, datetime.min.time())
            
            validator = PredictionValidator()
            results = validator.run_validation(validation_datetime)
            
            if results:
                success_count += 1
                print(f"   ✅ Success: {results['overall']['total_predictions']} predictions validated")
            else:
                print(f"   ❌ Failed: No data found")
        
        print(f"\n🎯 RANGE VALIDATION SUMMARY:")
        print(f"   📊 Days Processed: {len(dates)}")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Failed: {len(dates) - success_count}")
        print(f"   📈 Success Rate: {(success_count/len(dates)*100):.1f}%")
        
    else:
        # Run single day validation
        success = run_daily_validation()
        if not success:
            exit(1)

if __name__ == "__main__":
    main()
