#!/usr/bin/env python3
"""
🏀 NBA_API BOXSCORE INTEGRATION SERVICE
Automated WNBA boxscore retrieval for HYPER_MEDUSA_NEURAL_VAULT
"""

import os
import time
import pandas as pd
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
import json

try:
    from nba_api.stats.endpoints import boxscoretraditionalv2, scoreboardv2
    from nba_api.stats.static import teams
    NBA_API_AVAILABLE = True
except ImportError:
    NBA_API_AVAILABLE = False
    print("⚠️ NBA API not installed. Install with: pip install nba_api")

# Configuration
WNBA_LEAGUE_ID = '10'  # NBA API code for WNBA
DATA_PATH = 'data/boxscores'
LOG_PATH = 'logs/boxscore_service.log'

class BoxscoreConfig:
    """Configuration for boxscore service"""
    
    SETTINGS = {
        'check_interval': 900,  # 15 minutes
        'retry_policy': {
            'max_attempts': 5,
            'backoff': 300  # 5 minutes between retries
        },
        'data_retention_days': 30,
        'notifications': {
            'success': False,
            'failure': True
        },
        'rate_limiting': {
            'requests_per_minute': 10,
            'delay_between_requests': 6  # seconds
        }
    }

class BoxscoreService:
    """Main boxscore retrieval service"""
    
    def __init__(self, config=None):
        self.config = config or BoxscoreConfig.SETTINGS
        self.setup_logging()
        self.setup_directories()
        self.wnba_teams = self.get_wnba_teams()
        
    def setup_logging(self):
        """Setup logging configuration"""
        os.makedirs(os.path.dirname(LOG_PATH), exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_PATH, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_directories(self):
        """Create necessary directories"""
        directories = [DATA_PATH, 'logs', 'backups/boxscores']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            
    def get_wnba_teams(self):
        """Get all WNBA teams with their IDs"""
        if not NBA_API_AVAILABLE:
            return self.get_fallback_wnba_teams()
        
        try:
            # NBA API doesn't directly provide WNBA teams, so use fallback
            self.logger.info(f"Using fallback WNBA team data")
            return self.get_fallback_wnba_teams()
        except Exception as e:
            self.logger.error(f"Error fetching WNBA teams: {e}")
            return self.get_fallback_wnba_teams()

    def get_fallback_wnba_teams(self):
        """Get fallback WNBA team data"""
        return [
            {'id': 1611661313, 'full_name': 'Atlanta Dream', 'abbreviation': 'ATL'},
            {'id': 1611661314, 'full_name': 'Chicago Sky', 'abbreviation': 'CHI'},
            {'id': 1611661315, 'full_name': 'Connecticut Sun', 'abbreviation': 'CONN'},
            {'id': 1611661316, 'full_name': 'Dallas Wings', 'abbreviation': 'DAL'},
            {'id': 1611661317, 'full_name': 'Indiana Fever', 'abbreviation': 'IND'},
            {'id': 1611661318, 'full_name': 'Las Vegas Aces', 'abbreviation': 'LV'},
            {'id': 1611661319, 'full_name': 'Minnesota Lynx', 'abbreviation': 'MIN'},
            {'id': 1611661320, 'full_name': 'New York Liberty', 'abbreviation': 'NY'},
            {'id': 1611661321, 'full_name': 'Phoenix Mercury', 'abbreviation': 'PHX'},
            {'id': 1611661322, 'full_name': 'Seattle Storm', 'abbreviation': 'SEA'},
            {'id': 1611661323, 'full_name': 'Washington Mystics', 'abbreviation': 'WAS'},
            {'id': 1611661324, 'full_name': 'Golden State Valkyries', 'abbreviation': 'GS'},
            {'id': 1611661325, 'full_name': 'Toronto Tempo', 'abbreviation': 'TOR'}
        ]
    
    def get_todays_games(self, date=None):
        """Get today's scheduled WNBA games"""
        if not NBA_API_AVAILABLE:
            # Return simulated game data for testing
            return self.get_simulated_games(date)
        
        try:
            target_date = date or datetime.today().strftime('%Y-%m-%d')
            self.logger.info(f"🔍 Checking games for {target_date}")
            
            board = scoreboardv2.ScoreboardV2(league_id=WNBA_LEAGUE_ID, day_offset=0)
            games_df = board.get_data_frames()[0]
            
            # Filter for completed games
            completed_games = games_df[games_df['GAME_STATUS_TEXT'] == 'Final']
            self.logger.info(f"🏀 Found {len(completed_games)} completed games")
            
            return completed_games
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching games: {e}")
            return pd.DataFrame()
    
    def get_simulated_games(self, date=None):
        """Generate simulated game data for testing"""
        target_date = date or datetime.today().strftime('%Y-%m-%d')
        
        # Simulate 2 games for testing
        simulated_games = pd.DataFrame({
            'GAME_ID': ['0012300001', '0012300002'],
            'MATCHUP': ['GS @ DAL', 'PHX @ NY'],
            'GAME_STATUS_TEXT': ['Final', 'Final'],
            'HOME_TEAM_ID': [1611661316, 1611661320],
            'VISITOR_TEAM_ID': [1611661325, 1611661321],
            'GAME_DATE': [target_date, target_date]
        })
        
        self.logger.info(f"🎮 Generated {len(simulated_games)} simulated games for testing")
        return simulated_games
    
    def fetch_boxscore(self, game_id):
        """Retrieve boxscore for a specific game"""
        if not NBA_API_AVAILABLE:
            return self.generate_simulated_boxscore(game_id)
        
        try:
            self.logger.info(f"📊 Fetching boxscore for game {game_id}")
            
            # Rate limiting
            time.sleep(self.config['rate_limiting']['delay_between_requests'])
            
            boxscore = boxscoretraditionalv2.BoxScoreTraditionalV2(game_id=game_id)
            player_stats = boxscore.player_stats.get_data_frame()
            team_stats = boxscore.team_stats.get_data_frame()
            
            self.logger.info(f"✅ Retrieved {len(player_stats)} player records, {len(team_stats)} team records")
            return player_stats, team_stats
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching boxscore {game_id}: {e}")
            raise
    
    def generate_simulated_boxscore(self, game_id):
        """Generate simulated boxscore data for testing"""
        import random
        
        # Simulate player stats
        players = [
            'Alyssa Thomas', 'Breanna Stewart', 'A\'ja Wilson', 'Diana Taurasi',
            'Sabrina Ionescu', 'Kelsey Plum', 'Napheesa Collier', 'Jewell Loyd'
        ]
        
        player_data = []
        for i, player in enumerate(players):
            player_data.append({
                'PLAYER_ID': 200000 + i,
                'PLAYER_NAME': player,
                'TEAM_ID': 1611661313 + (i % 2),
                'TEAM_ABBREVIATION': 'GS' if i % 2 == 0 else 'DAL',
                'MIN': f"{random.randint(25, 40)}:{random.randint(10, 59):02d}",
                'PTS': random.randint(8, 35),
                'REB': random.randint(3, 15),
                'AST': random.randint(1, 12),
                'STL': random.randint(0, 4),
                'BLK': random.randint(0, 3),
                'FG3M': random.randint(0, 6),
                'FG3A': random.randint(0, 10),
                'FGM': random.randint(3, 15),
                'FGA': random.randint(8, 25),
                'FTM': random.randint(0, 8),
                'FTA': random.randint(0, 10)
            })
        
        player_stats = pd.DataFrame(player_data)
        
        # Simulate team stats
        team_stats = pd.DataFrame({
            'TEAM_ID': [1611661325, 1611661316],  # GS vs DAL
            'TEAM_NAME': ['Golden State Valkyries', 'Dallas Wings'],
            'TEAM_ABBREVIATION': ['GS', 'DAL'],
            'PTS': [random.randint(75, 95), random.randint(75, 95)],
            'FGM': [random.randint(25, 40), random.randint(25, 40)],
            'FGA': [random.randint(60, 85), random.randint(60, 85)],
            'FG_PCT': [random.uniform(0.35, 0.55), random.uniform(0.35, 0.55)],
            'REB': [random.randint(30, 45), random.randint(30, 45)],
            'AST': [random.randint(15, 25), random.randint(15, 25)]
        })
        
        self.logger.info(f"🎮 Generated simulated boxscore for game {game_id}")
        return player_stats, team_stats
    
    def save_boxscore(self, game_id, player_stats, team_stats):
        """Save boxscore data in efficient parquet format"""
        try:
            # Save player stats
            player_file = f'{DATA_PATH}/{game_id}_players.parquet'
            player_stats.to_parquet(player_file, index=False)
            
            # Save team stats
            team_file = f'{DATA_PATH}/{game_id}_teams.parquet'
            team_stats.to_parquet(team_file, index=False)
            
            # Save metadata
            metadata = {
                'game_id': game_id,
                'retrieved_at': datetime.now().isoformat(),
                'player_records': len(player_stats),
                'team_records': len(team_stats),
                'file_size_kb': {
                    'players': os.path.getsize(player_file) / 1024,
                    'teams': os.path.getsize(team_file) / 1024
                }
            }
            
            metadata_file = f'{DATA_PATH}/{game_id}_metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            self.logger.info(f"💾 Saved boxscore for game {game_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error saving boxscore {game_id}: {e}")
            return False
    
    def process_game(self, game_id, matchup="Unknown"):
        """Process a single game with retry logic"""
        max_attempts = self.config['retry_policy']['max_attempts']
        backoff = self.config['retry_policy']['backoff']
        
        for attempt in range(1, max_attempts + 1):
            try:
                self.logger.info(f"🎯 Processing {matchup} (Game {game_id}) - Attempt {attempt}/{max_attempts}")
                
                player_stats, team_stats = self.fetch_boxscore(game_id)
                
                if self.save_boxscore(game_id, player_stats, team_stats):
                    self.logger.info(f"✅ Successfully processed {matchup}")
                    return True
                    
            except Exception as e:
                self.logger.warning(f"⚠️ Attempt {attempt} failed for {game_id}: {e}")
                
                if attempt < max_attempts:
                    self.logger.info(f"⏳ Waiting {backoff}s before retry...")
                    time.sleep(backoff)
                else:
                    self.logger.error(f"❌ All attempts failed for {game_id}")
        
        return False
    
    def cleanup_old_files(self):
        """Remove old boxscore files based on retention policy"""
        try:
            retention_days = self.config['data_retention_days']
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            removed_count = 0
            for file_path in Path(DATA_PATH).glob('*.parquet'):
                if file_path.stat().st_mtime < cutoff_date.timestamp():
                    file_path.unlink()
                    removed_count += 1
            
            if removed_count > 0:
                self.logger.info(f"🧹 Cleaned up {removed_count} old files")
                
        except Exception as e:
            self.logger.error(f"❌ Error during cleanup: {e}")
    
    def run_single_check(self, date=None):
        """Run a single check for completed games"""
        self.logger.info("🔄 Starting boxscore check...")
        
        completed_games = self.get_todays_games(date)
        
        if completed_games.empty:
            self.logger.info("📭 No completed games found")
            return 0
        
        processed_count = 0
        
        for _, game in completed_games.iterrows():
            game_id = game['GAME_ID']
            matchup = game.get('MATCHUP', 'Unknown')
            
            # Check if already processed
            player_file = f'{DATA_PATH}/{game_id}_players.parquet'
            if os.path.exists(player_file):
                self.logger.info(f"⏭️ Skipping {matchup} - already processed")
                continue
            
            if self.process_game(game_id, matchup):
                processed_count += 1
        
        self.logger.info(f"📊 Processed {processed_count} new games")
        return processed_count
    
    def run_daemon(self):
        """Run the service in daemon mode"""
        self.logger.info("🚀 Starting boxscore service daemon...")
        self.logger.info(f"⏰ Check interval: {self.config['check_interval']}s")
        
        try:
            while True:
                self.run_single_check()
                self.cleanup_old_files()
                
                self.logger.info(f"😴 Sleeping for {self.config['check_interval']}s...")
                time.sleep(self.config['check_interval'])
                
        except KeyboardInterrupt:
            self.logger.info("🛑 Service stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Service error: {e}")

def main():
    parser = argparse.ArgumentParser(description="🏀 WNBA Boxscore Service")
    parser.add_argument("--daemon", action="store_true", help="Run in daemon mode")
    parser.add_argument("--single", action="store_true", help="Run single check")
    parser.add_argument("--date", help="Specific date (YYYY-MM-DD)")
    parser.add_argument("--status", action="store_true", help="Check service status")
    parser.add_argument("--cleanup", action="store_true", help="Run cleanup only")
    
    args = parser.parse_args()
    
    service = BoxscoreService()
    
    if args.status:
        print("📊 Boxscore Service Status:")
        print(f"   📁 Data Path: {DATA_PATH}")
        print(f"   📝 Log Path: {LOG_PATH}")
        print(f"   🏀 WNBA Teams: {len(service.wnba_teams)}")
        print(f"   🔧 NBA API: {'✅ Available' if NBA_API_AVAILABLE else '❌ Not Available'}")
        
        # Count existing files
        if os.path.exists(DATA_PATH):
            files = list(Path(DATA_PATH).glob('*_players.parquet'))
            print(f"   📦 Stored Games: {len(files)}")
        
    elif args.cleanup:
        service.cleanup_old_files()
        
    elif args.single:
        service.run_single_check(args.date)
        
    elif args.daemon:
        service.run_daemon()
        
    else:
        print("🏀 WNBA Boxscore Service")
        print("Use --daemon, --single, --status, or --cleanup")

if __name__ == "__main__":
    main()
