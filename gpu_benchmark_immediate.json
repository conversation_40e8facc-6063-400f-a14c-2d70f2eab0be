{"timestamp": "2025-07-07T18:04:10.768740", "device_info": {"cuda_available": true, "device_count": 1, "pytorch_version": "2.7.1+cu126", "device_name": "NVIDIA GeForce RTX 4050 Laptop GPU", "memory_total": 6.438780928, "compute_capability": "8.9"}, "benchmarks": {"latency": {"batch_1": {"avg_latency_ms": 0.126, "iterations": 119137, "throughput_per_sec": 7942.44}, "batch_8": {"avg_latency_ms": 0.114, "iterations": 131390, "throughput_per_sec": 8759.29}, "batch_16": {"avg_latency_ms": 0.126, "iterations": 119139, "throughput_per_sec": 7942.6}, "batch_32": {"avg_latency_ms": 0.128, "iterations": 117594, "throughput_per_sec": 7839.58}}, "throughput": {"total_predictions": 43527, "duration_seconds": 30.0, "predictions_per_minute": 87053.48, "predictions_per_second": 1450.89}, "memory": {"baseline_gb": 0.009, "current_gb": 0.042, "peak_gb": 0.042, "total_available_gb": 6.439, "utilization_percent": 0.65}, "mixed_precision": {"mse_loss": 5e-06, "max_difference": 0.013603, "accuracy_match_percent": 99.999}}, "summary": {"avg_latency_ms": 0.126, "throughput_rpm": 87053.48, "gpu_memory_gb": 0.042, "accuracy_percent": 99.999, "status": "SUCCESS"}}