#!/usr/bin/env python3
"""
🛡️ MODEL LOADER GUARDIAN
Bulletproof model loading system with comprehensive verification
"""

import json
import torch
import hashlib
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ModelGuardian:
    """Guardian system for bulletproof model loading"""
    
    def __init__(self, config_file='config/model_registry.json'):
        self.config_file = Path(config_file)
        self.registry = self._load_registry()
        self.loaded_models = {}
        self.verification_log = []
    
    def _load_registry(self):
        """Load model registry with expected models"""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                return json.load(f)
        else:
            # Create default registry
            default_registry = {
                "models": {
                    "step1_points": {
                        "path": "models/player_points_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 1: Player Points Model",
                        "required": True,
                        "expected_size_mb": 50,
                        "last_trained": "2025-07-07"
                    },
                    "step2_totals": {
                        "path": "models/game_totals_model.ckpt", 
                        "type": "pytorch_lightning",
                        "description": "Step 2: Game Totals Model",
                        "required": True,
                        "expected_size_mb": 45,
                        "last_trained": "2025-07-07"
                    },
                    "step3_moneyline": {
                        "path": "models/moneyline_model.ckpt",
                        "type": "pytorch_lightning", 
                        "description": "Step 3: Moneyline Model",
                        "required": True,
                        "expected_size_mb": 40,
                        "last_trained": "2025-07-07"
                    },
                    "step4_rebounds": {
                        "path": "models/player_rebounds_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 4: Player Rebounds Model", 
                        "required": True,
                        "expected_size_mb": 50,
                        "last_trained": "2025-07-07"
                    },
                    "step5_assists": {
                        "path": "models/player_assists_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 5: Player Assists Model",
                        "required": True,
                        "expected_size_mb": 50,
                        "last_trained": "2025-07-07"
                    },
                    "step6_assists_v2": {
                        "path": "models/step6_assists_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 6: Enhanced Assists Model",
                        "required": True,
                        "expected_size_mb": 55,
                        "last_trained": "2025-07-07"
                    },
                    "step7_threes": {
                        "path": "models/step7_threes_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 7: Three-Pointers Model",
                        "required": True,
                        "expected_size_mb": 60,
                        "last_trained": "2025-07-07"
                    },
                    "step8_steals": {
                        "path": "models/step8_steals_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 8: Steals Model",
                        "required": True,
                        "expected_size_mb": 45,
                        "last_trained": "2025-07-07"
                    },
                    "step8_blocks": {
                        "path": "models/step8_blocks_model.ckpt",
                        "type": "pytorch_lightning",
                        "description": "Step 8: Blocks Model",
                        "required": True,
                        "expected_size_mb": 45,
                        "last_trained": "2025-07-07"
                    }
                },
                "verification": {
                    "check_file_exists": True,
                    "check_file_size": True,
                    "check_pytorch_load": True,
                    "check_model_structure": True,
                    "require_all_models": True
                }
            }
            self._save_registry(default_registry)
            return default_registry
    
    def _save_registry(self, registry):
        """Save model registry"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(registry, f, indent=2)
    
    def verify_model_file(self, model_key, model_info):
        """Comprehensive model file verification"""
        verification = {
            'model_key': model_key,
            'path': model_info['path'],
            'checks': {},
            'status': 'UNKNOWN',
            'errors': [],
            'warnings': []
        }
        
        model_path = Path(model_info['path'])
        
        # Check 1: File exists
        if self.registry['verification']['check_file_exists']:
            if model_path.exists():
                verification['checks']['file_exists'] = True
            else:
                verification['checks']['file_exists'] = False
                verification['errors'].append(f"Model file not found: {model_path}")
        
        # Check 2: File size reasonable
        if self.registry['verification']['check_file_size'] and model_path.exists():
            file_size_mb = model_path.stat().st_size / (1024 * 1024)
            expected_size = model_info.get('expected_size_mb', 50)
            
            if file_size_mb < expected_size * 0.1:  # Less than 10% of expected
                verification['checks']['file_size'] = False
                verification['errors'].append(f"File too small: {file_size_mb:.1f}MB (expected ~{expected_size}MB)")
            elif file_size_mb > expected_size * 3:  # More than 3x expected
                verification['checks']['file_size'] = False
                verification['warnings'].append(f"File larger than expected: {file_size_mb:.1f}MB (expected ~{expected_size}MB)")
            else:
                verification['checks']['file_size'] = True
                verification['file_size_mb'] = round(file_size_mb, 1)
        
        # Check 3: PyTorch can load the file
        if self.registry['verification']['check_pytorch_load'] and model_path.exists():
            try:
                # Try to load checkpoint
                checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
                verification['checks']['pytorch_load'] = True
                
                # Extract metadata if available
                if isinstance(checkpoint, dict):
                    if 'epoch' in checkpoint:
                        verification['epoch'] = checkpoint['epoch']
                    if 'state_dict' in checkpoint:
                        verification['has_state_dict'] = True
                    if 'optimizer_states' in checkpoint:
                        verification['has_optimizer'] = True
                
            except Exception as e:
                verification['checks']['pytorch_load'] = False
                verification['errors'].append(f"PyTorch load failed: {str(e)}")
        
        # Check 4: Model structure validation
        if self.registry['verification']['check_model_structure'] and model_path.exists():
            try:
                checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
                if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                    param_count = len(state_dict)
                    verification['checks']['model_structure'] = True
                    verification['parameter_count'] = param_count
                    
                    # Check for reasonable parameter count
                    if param_count < 10:
                        verification['warnings'].append(f"Low parameter count: {param_count}")
                else:
                    verification['checks']['model_structure'] = False
                    verification['errors'].append("No state_dict found in checkpoint")
            except Exception as e:
                verification['checks']['model_structure'] = False
                verification['errors'].append(f"Structure check failed: {str(e)}")
        
        # Determine overall status
        if verification['errors']:
            verification['status'] = 'FAILED'
        elif verification['warnings']:
            verification['status'] = 'WARNING'
        else:
            verification['status'] = 'PASSED'
        
        return verification
    
    def verify_all_models(self):
        """Verify all models in registry"""
        print("🛡️ MODEL GUARDIAN VERIFICATION")
        print("=" * 50)
        
        results = {
            'total_models': len(self.registry['models']),
            'passed': 0,
            'warnings': 0,
            'failed': 0,
            'details': []
        }
        
        for model_key, model_info in self.registry['models'].items():
            print(f"🔍 Verifying {model_key}...")
            verification = self.verify_model_file(model_key, model_info)
            results['details'].append(verification)
            
            if verification['status'] == 'PASSED':
                results['passed'] += 1
                print(f"   ✅ {verification['status']}")
            elif verification['status'] == 'WARNING':
                results['warnings'] += 1
                print(f"   ⚠️ {verification['status']}")
            else:
                results['failed'] += 1
                print(f"   ❌ {verification['status']}")
            
            # Show errors/warnings
            for error in verification['errors']:
                print(f"      ❌ {error}")
            for warning in verification['warnings']:
                print(f"      ⚠️ {warning}")
        
        print(f"\n📊 VERIFICATION SUMMARY:")
        print(f"   📊 Total Models: {results['total_models']}")
        print(f"   ✅ Passed: {results['passed']}")
        print(f"   ⚠️ Warnings: {results['warnings']}")
        print(f"   ❌ Failed: {results['failed']}")
        
        success_rate = (results['passed'] / results['total_models']) * 100
        print(f"   📈 Success Rate: {success_rate:.1f}%")
        
        # Overall status
        if results['failed'] == 0:
            if results['warnings'] == 0:
                print(f"   🎯 Overall Status: ✅ ALL MODELS VERIFIED")
            else:
                print(f"   🎯 Overall Status: ⚠️ WARNINGS DETECTED")
        else:
            print(f"   🎯 Overall Status: ❌ CRITICAL FAILURES")
        
        return results
    
    def safe_load_model(self, model_key):
        """Safely load a specific model with verification"""
        if model_key not in self.registry['models']:
            raise ValueError(f"Unknown model key: {model_key}")
        
        model_info = self.registry['models'][model_key]
        
        # Verify before loading
        verification = self.verify_model_file(model_key, model_info)
        
        if verification['status'] == 'FAILED':
            raise RuntimeError(f"Model verification failed for {model_key}: {verification['errors']}")
        
        # Load the model
        try:
            model_path = Path(model_info['path'])
            checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
            
            # Store in loaded models cache
            self.loaded_models[model_key] = {
                'checkpoint': checkpoint,
                'path': str(model_path),
                'loaded_at': datetime.now().isoformat(),
                'verification': verification
            }
            
            print(f"✅ Successfully loaded: {model_key}")
            return checkpoint
            
        except Exception as e:
            raise RuntimeError(f"Failed to load {model_key}: {str(e)}")
    
    def get_model_status(self):
        """Get status of all models"""
        return {
            'registry_models': len(self.registry['models']),
            'loaded_models': len(self.loaded_models),
            'loaded_list': list(self.loaded_models.keys()),
            'last_verification': datetime.now().isoformat()
        }

def main():
    """Command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description="🛡️ Model Guardian")
    parser.add_argument("--verify", action="store_true", help="Verify all models")
    parser.add_argument("--load", help="Load specific model")
    parser.add_argument("--status", action="store_true", help="Show model status")
    parser.add_argument("--update-registry", action="store_true", help="Update model registry")
    
    args = parser.parse_args()
    
    guardian = ModelGuardian()
    
    if args.verify:
        results = guardian.verify_all_models()
        if results['failed'] > 0:
            exit(1)
    
    elif args.load:
        try:
            checkpoint = guardian.safe_load_model(args.load)
            print(f"✅ Model {args.load} loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load {args.load}: {e}")
            exit(1)
    
    elif args.status:
        status = guardian.get_model_status()
        print("🛡️ MODEL GUARDIAN STATUS")
        print("=" * 30)
        for key, value in status.items():
            print(f"   📊 {key.replace('_', ' ').title()}: {value}")
    
    else:
        # Default: verify all models
        guardian.verify_all_models()

if __name__ == "__main__":
    main()
