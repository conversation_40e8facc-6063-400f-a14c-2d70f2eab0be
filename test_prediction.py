#!/usr/bin/env python3
"""
🧪 PREDICTION TEST SCRIPT
Test individual predictions for HYPER_MEDUSA_NEURAL_VAULT
"""

import requests
import json
import time
import argparse
from datetime import datetime

def test_prediction(player_id=203, stat="points", team_id=1, base_url="http://localhost:8080"):
    """Test a specific prediction"""
    
    print(f"🧪 TESTING PREDICTION: {stat.upper()}")
    print("=" * 40)
    
    # Map stat to model name
    stat_to_model = {
        "points": "player_points",
        "rebounds": "rebounds", 
        "assists": "assists",
        "threes": "threes",
        "steals": "steals",
        "blocks": "blocks",
        "totals": "game_totals",
        "moneyline": "moneyline"
    }
    
    model_name = stat_to_model.get(stat.lower())
    if not model_name:
        print(f"❌ Unknown stat: {stat}")
        print(f"Available stats: {', '.join(stat_to_model.keys())}")
        return False
    
    # Build request
    if model_name in ["game_totals", "moneyline"]:
        request_data = {
            "model": model_name,
            "team_id": team_id
        }
    else:
        request_data = {
            "model": model_name,
            "player_id": player_id
        }
    
    print(f"📋 Request: {json.dumps(request_data, indent=2)}")
    print(f"🌐 URL: {base_url}/predict")
    
    try:
        # Make prediction request
        start_time = time.time()
        response = requests.post(f"{base_url}/predict", json=request_data, timeout=10)
        end_time = time.time()
        
        latency_ms = (end_time - start_time) * 1000
        
        if response.status_code == 200:
            prediction_data = response.json()
            
            print(f"\n✅ PREDICTION SUCCESS")
            print(f"   🎯 Value: {prediction_data['prediction']}")
            print(f"   📊 Confidence: {prediction_data['confidence']:.1%}")
            print(f"   ⚡ Latency: {latency_ms:.2f}ms")
            print(f"   🧠 Model Loaded: {prediction_data.get('model_loaded', 'Unknown')}")
            print(f"   📁 Model File: {prediction_data.get('model_file', 'Unknown')}")
            print(f"   🖥️ Device: {prediction_data.get('device', 'Unknown')}")
            print(f"   ⏰ Timestamp: {prediction_data.get('timestamp', 'Unknown')}")
            
            if prediction_data.get('fallback'):
                print(f"   ⚠️ Fallback Mode: {prediction_data.get('warning', '')}")
            
            # Interpretation
            print(f"\n🔍 INTERPRETATION:")
            if stat.lower() == "points":
                if prediction_data['prediction'] > 20:
                    print("   🔥 High scoring performance expected")
                elif prediction_data['prediction'] > 15:
                    print("   📈 Above average scoring expected")
                else:
                    print("   📉 Below average scoring expected")
            elif stat.lower() == "moneyline":
                win_prob = prediction_data['prediction']
                if win_prob > 0.6:
                    print(f"   🏆 Strong favorite ({win_prob:.1%} win probability)")
                elif win_prob > 0.4:
                    print(f"   ⚖️ Competitive game ({win_prob:.1%} win probability)")
                else:
                    print(f"   📉 Underdog ({win_prob:.1%} win probability)")
            elif stat.lower() == "totals":
                total_points = prediction_data['prediction']
                if total_points > 170:
                    print("   🔥 High-scoring game expected")
                elif total_points > 160:
                    print("   📈 Above average scoring expected")
                else:
                    print("   📉 Lower scoring game expected")
            
            return True
            
        else:
            print(f"\n❌ PREDICTION FAILED")
            print(f"   📄 Status Code: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"\n⏰ REQUEST TIMEOUT")
        print("   Server may be overloaded or not responding")
        return False
    except requests.exceptions.ConnectionError:
        print(f"\n🔌 CONNECTION ERROR")
        print("   Server may not be running")
        print(f"   Check: {base_url}/health")
        return False
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR")
        print(f"   Error: {str(e)}")
        return False

def test_server_health(base_url="http://localhost:8080"):
    """Test server health"""
    print("🏥 CHECKING SERVER HEALTH")
    print("-" * 30)
    
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Status: {health_data['status']}")
            print(f"🖥️ Device: {health_data['device']}")
            print(f"⚡ GPU Available: {health_data['gpu_available']}")
            print(f"⏱️ Uptime: {health_data['uptime_seconds']:.1f}s")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")
        return False

def test_multiple_predictions(base_url="http://localhost:8080"):
    """Test multiple predictions"""
    print("🎯 TESTING MULTIPLE PREDICTIONS")
    print("=" * 40)
    
    test_cases = [
        {"player_id": 203, "stat": "points"},
        {"player_id": 204, "stat": "rebounds"},
        {"player_id": 205, "stat": "assists"},
        {"team_id": 1, "stat": "totals"},
        {"team_id": 2, "stat": "moneyline"}
    ]
    
    results = []
    total_time = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}/{len(test_cases)}: {test_case}")
        
        start_time = time.time()
        success = test_prediction(base_url=base_url, **test_case)
        end_time = time.time()
        
        test_time = end_time - start_time
        total_time += test_time
        
        results.append({
            "test_case": test_case,
            "success": success,
            "time_seconds": test_time
        })
        
        if i < len(test_cases):
            print("\n" + "-" * 40)
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 40)
    successful = sum(1 for r in results if r['success'])
    print(f"✅ Successful: {successful}/{len(results)}")
    print(f"⏱️ Total Time: {total_time:.2f}s")
    print(f"📈 Average Time: {total_time/len(results):.2f}s per test")
    print(f"🎯 Success Rate: {successful/len(results)*100:.1f}%")
    
    return successful == len(results)

def main():
    parser = argparse.ArgumentParser(description="🧪 Test HYPER_MEDUSA_NEURAL_VAULT predictions")
    parser.add_argument("--player", type=int, default=203, help="Player ID")
    parser.add_argument("--stat", default="points", help="Stat to predict")
    parser.add_argument("--team", type=int, default=1, help="Team ID")
    parser.add_argument("--url", default="http://localhost:8080", help="Server URL")
    parser.add_argument("--health", action="store_true", help="Check server health only")
    parser.add_argument("--multiple", action="store_true", help="Test multiple predictions")
    
    args = parser.parse_args()
    
    print("🧪 HYPER_MEDUSA_NEURAL_VAULT PREDICTION TEST")
    print("=" * 50)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Server: {args.url}")
    
    # Check server health first
    if not test_server_health(args.url):
        print("\n❌ Server health check failed - aborting tests")
        return
    
    if args.health:
        print("\n✅ Health check complete")
        return
    
    print("\n")
    
    if args.multiple:
        success = test_multiple_predictions(args.url)
    else:
        success = test_prediction(
            player_id=args.player,
            stat=args.stat,
            team_id=args.team,
            base_url=args.url
        )
    
    print(f"\n🏁 TEST COMPLETE")
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    print(f"⏰ Finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
