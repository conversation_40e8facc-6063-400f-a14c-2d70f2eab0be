#!/usr/bin/env python3
"""
🧪 PREDICTION VALIDATION TESTING SYSTEM
Test the validation system with sample data
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from prediction_analyzer import PredictionV<PERSON><PERSON><PERSON>

def create_sample_predictions(date, output_dir='predictions/daily'):
    """Create sample predictions for testing"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    date_str = date.strftime('%Y-%m-%d')
    
    # Sample WNBA predictions
    sample_predictions = {
        f"game_1022500119_{date_str}": {
            "game_info": {
                "game_id": "1022500119",
                "date": date_str,
                "home_team": "Las Vegas Aces",
                "away_team": "Atlanta Dream"
            },
            "player_props": {
                "A'ja <PERSON>": {
                    "points": 24.5,
                    "rebounds": 11.2,
                    "assists": 3.1,
                    "blocks": 1.8
                },
                "<PERSON> Plum": {
                    "points": 18.3,
                    "rebounds": 3.2,
                    "assists": 5.7,
                    "threes": 2.4
                },
                "<PERSON><PERSON><PERSON>": {
                    "points": 16.8,
                    "rebounds": 4.1,
                    "assists": 3.9,
                    "steals": 1.2
                },
                "Allisha Gray": {
                    "points": 14.2,
                    "rebounds": 5.3,
                    "assists": 2.8,
                    "threes": 1.9
                }
            },
            "game_outcomes": {
                "moneyline": 0.72,  # 72% chance home team wins
                "spread": -8.5,     # Home team favored by 8.5
                "total": 162.3      # Total points prediction
            }
        },
        f"game_1022500120_{date_str}": {
            "game_info": {
                "game_id": "1022500120",
                "date": date_str,
                "home_team": "Phoenix Mercury",
                "away_team": "Dallas Wings"
            },
            "player_props": {
                "Diana Taurasi": {
                    "points": 15.7,
                    "rebounds": 3.8,
                    "assists": 4.2,
                    "threes": 2.1
                },
                "Brittney Griner": {
                    "points": 19.4,
                    "rebounds": 8.9,
                    "assists": 1.6,
                    "blocks": 1.4
                },
                "Arike Ogunbowale": {
                    "points": 21.3,
                    "rebounds": 4.2,
                    "assists": 5.1,
                    "steals": 1.3
                },
                "Satou Sabally": {
                    "points": 17.6,
                    "rebounds": 7.8,
                    "assists": 3.4,
                    "blocks": 0.9
                }
            },
            "game_outcomes": {
                "moneyline": 0.58,  # 58% chance home team wins
                "spread": -3.5,     # Home team favored by 3.5
                "total": 158.7      # Total points prediction
            }
        }
    }
    
    # Save predictions
    prediction_file = output_path / f'{date_str}_predictions.json'
    with open(prediction_file, 'w') as f:
        json.dump(sample_predictions, f, indent=2)
    
    print(f"✅ Created sample predictions: {prediction_file}")
    return prediction_file

def create_sample_boxscores(date, output_dir='data/boxscores'):
    """Create sample boxscores for testing"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Game 1: Las Vegas Aces vs Atlanta Dream
    game1_players = pd.DataFrame([
        # Las Vegas Aces
        {'PLAYER_NAME': "A'ja Wilson", 'TEAM_ABBREVIATION': 'LV', 'PTS': 26, 'REB': 12, 'AST': 3, 'STL': 1, 'BLK': 2, 'FG3M': 0},
        {'PLAYER_NAME': 'Kelsey Plum', 'TEAM_ABBREVIATION': 'LV', 'PTS': 19, 'REB': 3, 'AST': 6, 'STL': 2, 'BLK': 0, 'FG3M': 3},
        {'PLAYER_NAME': 'Jackie Young', 'TEAM_ABBREVIATION': 'LV', 'PTS': 14, 'REB': 4, 'AST': 4, 'STL': 1, 'BLK': 0, 'FG3M': 2},
        {'PLAYER_NAME': 'Chelsea Gray', 'TEAM_ABBREVIATION': 'LV', 'PTS': 12, 'REB': 2, 'AST': 7, 'STL': 1, 'BLK': 0, 'FG3M': 1},
        
        # Atlanta Dream
        {'PLAYER_NAME': 'Rhyne Howard', 'TEAM_ABBREVIATION': 'ATL', 'PTS': 18, 'REB': 4, 'AST': 4, 'STL': 2, 'BLK': 0, 'FG3M': 2},
        {'PLAYER_NAME': 'Allisha Gray', 'TEAM_ABBREVIATION': 'ATL', 'PTS': 15, 'REB': 5, 'AST': 3, 'STL': 1, 'BLK': 0, 'FG3M': 2},
        {'PLAYER_NAME': 'Tina Charles', 'TEAM_ABBREVIATION': 'ATL', 'PTS': 13, 'REB': 8, 'AST': 2, 'STL': 0, 'BLK': 1, 'FG3M': 0},
        {'PLAYER_NAME': 'Jordin Canada', 'TEAM_ABBREVIATION': 'ATL', 'PTS': 8, 'REB': 3, 'AST': 5, 'STL': 2, 'BLK': 0, 'FG3M': 1}
    ])
    
    game1_teams = pd.DataFrame([
        {'TEAM_ABBREVIATION': 'LV', 'PTS': 89, 'REB': 35, 'AST': 22, 'STL': 8, 'BLK': 4},
        {'TEAM_ABBREVIATION': 'ATL', 'PTS': 76, 'REB': 32, 'AST': 18, 'STL': 7, 'BLK': 2}
    ])
    
    # Game 2: Phoenix Mercury vs Dallas Wings
    game2_players = pd.DataFrame([
        # Phoenix Mercury
        {'PLAYER_NAME': 'Diana Taurasi', 'TEAM_ABBREVIATION': 'PHX', 'PTS': 17, 'REB': 4, 'AST': 4, 'STL': 1, 'BLK': 0, 'FG3M': 3},
        {'PLAYER_NAME': 'Brittney Griner', 'TEAM_ABBREVIATION': 'PHX', 'PTS': 21, 'REB': 9, 'AST': 2, 'STL': 0, 'BLK': 2, 'FG3M': 0},
        {'PLAYER_NAME': 'Kahleah Copper', 'TEAM_ABBREVIATION': 'PHX', 'PTS': 16, 'REB': 5, 'AST': 3, 'STL': 2, 'BLK': 0, 'FG3M': 1},
        {'PLAYER_NAME': 'Sophie Cunningham', 'TEAM_ABBREVIATION': 'PHX', 'PTS': 11, 'REB': 3, 'AST': 2, 'STL': 1, 'BLK': 0, 'FG3M': 3},
        
        # Dallas Wings
        {'PLAYER_NAME': 'Arike Ogunbowale', 'TEAM_ABBREVIATION': 'DAL', 'PTS': 23, 'REB': 4, 'AST': 5, 'STL': 2, 'BLK': 0, 'FG3M': 4},
        {'PLAYER_NAME': 'Satou Sabally', 'TEAM_ABBREVIATION': 'DAL', 'PTS': 19, 'REB': 8, 'AST': 3, 'STL': 1, 'BLK': 1, 'FG3M': 1},
        {'PLAYER_NAME': 'Teaira McCowan', 'TEAM_ABBREVIATION': 'DAL', 'PTS': 12, 'REB': 10, 'AST': 1, 'STL': 0, 'BLK': 2, 'FG3M': 0},
        {'PLAYER_NAME': 'Sevgi Uzun', 'TEAM_ABBREVIATION': 'DAL', 'PTS': 9, 'REB': 2, 'AST': 6, 'STL': 1, 'BLK': 0, 'FG3M': 2}
    ])
    
    game2_teams = pd.DataFrame([
        {'TEAM_ABBREVIATION': 'PHX', 'PTS': 82, 'REB': 33, 'AST': 19, 'STL': 6, 'BLK': 3},
        {'TEAM_ABBREVIATION': 'DAL', 'PTS': 85, 'REB': 36, 'AST': 20, 'STL': 8, 'BLK': 4}
    ])
    
    # Save boxscores
    game1_players.to_parquet(output_path / '1022500119_players.parquet')
    game1_teams.to_parquet(output_path / '1022500119_teams.parquet')
    game2_players.to_parquet(output_path / '1022500120_players.parquet')
    game2_teams.to_parquet(output_path / '1022500120_teams.parquet')
    
    print(f"✅ Created sample boxscores in: {output_path}")
    return [
        output_path / '1022500119_players.parquet',
        output_path / '1022500119_teams.parquet',
        output_path / '1022500120_players.parquet',
        output_path / '1022500120_teams.parquet'
    ]

def run_validation_test(date=None, output_dir='test_reports'):
    """Run complete validation test"""
    if date is None:
        date = datetime.now() - timedelta(days=1)
    
    print("🧪 PREDICTION VALIDATION TEST")
    print("=" * 50)
    print(f"📅 Test Date: {date.strftime('%Y-%m-%d')}")
    print()
    
    # Create sample data
    print("📊 Creating sample data...")
    prediction_file = create_sample_predictions(date)
    boxscore_files = create_sample_boxscores(date)
    print()
    
    # Run validation
    print("🔍 Running validation...")
    validator = PredictionValidator(
        prediction_dir='predictions/daily',
        boxscore_dir='data/boxscores',
        output_dir=output_dir
    )
    
    results = validator.run_validation(date)
    print()
    
    if results:
        print("📈 VALIDATION RESULTS:")
        print(f"   📊 Total Predictions: {results['overall']['total_predictions']}")
        print(f"   👥 Player Props: {results['overall']['player_prop_count']}")
        print(f"   🏀 Game Outcomes: {results['overall']['game_outcome_count']}")
        print(f"   📈 Avg Player Error: {results['overall']['avg_player_error']}")
        print(f"   📈 Avg Game Error: {results['overall']['avg_game_error']}")
        print()
        
        # Player props details
        if results['player_props']:
            print("🎯 PLAYER PROPS ACCURACY:")
            for stat_type, metrics in results['player_props'].items():
                print(f"   {stat_type.title()}: {metrics['accuracy']}% (MAE: {metrics['mae']})")
            print()
        
        # Game outcomes details
        if results['game_outcomes']:
            print("🏀 GAME OUTCOMES ACCURACY:")
            for outcome_type, metrics in results['game_outcomes'].items():
                accuracy_key = 'accuracy' if 'accuracy' in metrics else 'cover_accuracy'
                accuracy = metrics.get(accuracy_key, 'N/A')
                mae = metrics.get('mae', 'N/A')
                print(f"   {outcome_type.title()}: {accuracy}% (MAE: {mae})")
            print()
        
        # Insights
        if results['insights']:
            print("💡 KEY INSIGHTS:")
            for insight in results['insights']:
                emoji = "⚠️" if insight['type'] == 'warning' else "✅"
                print(f"   {emoji} {insight['message']}")
                print(f"      → {insight['recommendation']}")
            print()
        
        # Report location
        report_path = Path(output_dir) / f"{date.strftime('%Y-%m-%d')}_validation_report.html"
        if report_path.exists():
            print(f"📄 Full report saved to: {report_path}")
            print(f"🌐 Open in browser: file://{report_path.absolute()}")
        
        return True
    else:
        print("❌ Validation failed - no results generated")
        return False

def cleanup_test_data():
    """Clean up test data"""
    print("🧹 Cleaning up test data...")
    
    # Remove test predictions
    pred_dir = Path('predictions/daily')
    if pred_dir.exists():
        for file in pred_dir.glob('*_predictions.json'):
            file.unlink()
            print(f"   🗑️ Removed: {file}")
    
    # Remove test boxscores
    box_dir = Path('data/boxscores')
    if box_dir.exists():
        for file in box_dir.glob('102250012*'):
            file.unlink()
            print(f"   🗑️ Removed: {file}")
    
    print("✅ Cleanup complete")

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="🧪 Prediction Validation Test")
    parser.add_argument("--date", help="Test date (YYYY-MM-DD)")
    parser.add_argument("--output", default="test_reports", help="Output directory")
    parser.add_argument("--cleanup", action="store_true", help="Clean up test data after run")
    parser.add_argument("--no-cleanup", action="store_true", help="Skip cleanup")
    
    args = parser.parse_args()
    
    # Parse date
    test_date = None
    if args.date:
        try:
            test_date = datetime.strptime(args.date, '%Y-%m-%d')
        except ValueError:
            print("❌ Invalid date format. Use YYYY-MM-DD")
            return
    
    # Run test
    success = run_validation_test(date=test_date, output_dir=args.output)
    
    # Cleanup if requested
    if args.cleanup and not args.no_cleanup:
        cleanup_test_data()
    elif not args.no_cleanup and success:
        response = input("\n🧹 Clean up test data? (y/N): ")
        if response.lower() in ['y', 'yes']:
            cleanup_test_data()
    
    if success:
        print("\n🎉 Validation test completed successfully!")
    else:
        print("\n❌ Validation test failed!")

if __name__ == "__main__":
    main()
