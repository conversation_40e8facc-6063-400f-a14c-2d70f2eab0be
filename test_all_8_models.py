#!/usr/bin/env python3
"""
🧪 HYPER_MEDUSA_NEURAL_VAULT - ALL 8 MODELS TEST
Test all 8 models in the system to ensure they load and predict correctly
"""

import requests
import json
import time
from datetime import datetime

def test_model(model_name, test_data):
    """Test a specific model"""
    print(f"\n🧪 TESTING MODEL: {model_name.upper()}")
    print("=" * 50)
    
    url = "http://localhost:8080/predict"
    
    try:
        start_time = time.time()
        response = requests.post(url, json=test_data, timeout=30)
        latency = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS")
            print(f"   🎯 Prediction: {result.get('prediction', 'N/A')}")
            print(f"   📊 Confidence: {result.get('confidence', 'N/A')}%")
            print(f"   ⚡ Latency: {latency:.2f}ms")
            print(f"   🧠 Model Loaded: {result.get('model_loaded', False)}")
            print(f"   📁 Model File: {result.get('model_file', 'N/A')}")
            print(f"   🖥️ Device: {result.get('device', 'N/A')}")
            return True
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            print(f"   📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def main():
    print("🚀 HYPER_MEDUSA_NEURAL_VAULT - ALL 8 MODELS TEST")
    print("=" * 60)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Server: http://localhost:8080")
    
    # Test data for each model
    test_cases = [
        {
            "name": "player_points",
            "data": {"model": "player_points", "player_id": 203}
        },
        {
            "name": "game_totals", 
            "data": {"model": "game_totals", "home_team": "LAS", "away_team": "ATL"}
        },
        {
            "name": "moneyline",
            "data": {"model": "moneyline", "home_team": "LAS", "away_team": "ATL"}
        },
        {
            "name": "rebounds",
            "data": {"model": "rebounds", "player_id": 203}
        },
        {
            "name": "assists", 
            "data": {"model": "assists", "player_id": 203}
        },
        {
            "name": "threes",
            "data": {"model": "threes", "player_id": 203}
        },
        {
            "name": "steals",
            "data": {"model": "steals", "player_id": 203}
        },
        {
            "name": "blocks",
            "data": {"model": "blocks", "player_id": 203}
        }
    ]
    
    # Test server health first
    print("\n🏥 CHECKING SERVER HEALTH")
    print("-" * 30)
    try:
        health_response = requests.get("http://localhost:8080/health", timeout=10)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Status: {health_data.get('status', 'unknown')}")
            print(f"🖥️ Device: {health_data.get('device', 'unknown')}")
            print(f"⚡ GPU Available: {health_data.get('gpu_available', False)}")
            print(f"⏱️ Uptime: {health_data.get('uptime', 'unknown')}")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test all models
    results = []
    for test_case in test_cases:
        success = test_model(test_case["name"], test_case["data"])
        results.append({"model": test_case["name"], "success": success})
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 40)
    successful = sum(1 for r in results if r["success"])
    total = len(results)
    
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"   {result['model']:15} {status}")
    
    print(f"\n🎯 OVERALL RESULT: {successful}/{total} models working")
    
    if successful == total:
        print("🎉 ALL MODELS OPERATIONAL!")
        print("🚀 HYPER_MEDUSA_NEURAL_VAULT READY FOR PRODUCTION")
    else:
        print(f"⚠️ {total - successful} models need attention")
    
    print(f"⏰ Finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
